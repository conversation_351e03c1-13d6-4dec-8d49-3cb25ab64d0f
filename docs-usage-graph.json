{"generated_at": "2025-06-15T22:25:26.644679", "dependencies": {"PROJECT_MAP.md": [], "PROJECT_CONVENTIONS.md": [], "PROJECT_NAVIGATION_SYSTEM.md": [], "STYLE_GUIDE.md": ["atoms/setup/environment.md", "workflows/testing.md", "atoms/setup/environment.md"], "README.md": ["PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/ASSESSMENT_SYSTEM.md", "project-management/GLOSSARY.md", "development/PHASE2_IMPLEMENTATION_SUMMARY.md", "development/PHASE3_IMPLEMENTATION_SUMMARY.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md", "development/NAVIGATION_ENHANCEMENT_REPORT.md", "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md", "development/ENHANCED_FEATURES_IMPLEMENTATION.md", "development/IMPLEMENTATION_SUMMARY.md", "development/AI_AGENT_TRANSITION_SUMMARY.md", "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "features/ENHANCED_ASSESSMENT_RESULTS.md", "features/ASSESSMENT_RESULTS_INTEGRATION.md", "user-guides/user-guide.md", "user-guides/API.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/03_TECH_SPECS.md", "project-management/02_ARCHITECTURE.md", "user-guides/API.md", "user-guides/troubleshooting-guide.md", "development/AI_AGENT_TRANSITION_SUMMARY.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "user-guides/API.md"], "PROJECT_STRUCTURE_GUIDE.md": [], "DOCUMENTATION_INDEX.md": ["PROJECT_MAP.md", "README.md"], "resource-improvement-summary.md": [], "atoms/procedures/deployment-checklist.md": [], "atoms/procedures/url-validation.md": [], "atoms/setup/environment.md": [], "atoms/setup/database-setup.md": [], "atoms/concepts/validation-system.md": [], "atoms/concepts/directory-structure.md": [], "atoms/concepts/naming-conventions.md": [], "atoms/concepts/testing-structure.md": [], "atoms/commands/testing.md": [], "atoms/commands/testerat.md": [], "atoms/commands/development.md": [], "development/PHASE3_IMPLEMENTATION_SUMMARY.md": [], "development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md": [], "development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md": [], "development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md": [], "development/CODE_QUALITY_FIXES_SUMMARY.md": [], "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": [], "development/URL_VALIDATION_IMPLEMENTATION.md": [], "development/PHASE1_IMPLEMENTATION_PLAN.md": [], "development/IMPLEMENTATION_SUMMARY.md": [], "development/IMPLEMENTATION_COMPLETE.md": [], "development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md": [], "development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md": [], "development/PHASE1_SETUP_GUIDE.md": [], "development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md": [], "development/PHASE1_IMPLEMENTATION_COMPLETE.md": [], "development/NAVIGATION_ENHANCEMENT_REPORT.md": [], "development/README.md": ["development/PHASE2_IMPLEMENTATION_SUMMARY.md", "development/PHASE3_IMPLEMENTATION_SUMMARY.md", "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md", "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md", "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "development/NAVIGATION_ENHANCEMENT_REPORT.md", "development/ENHANCED_FEATURES_IMPLEMENTATION.md", "development/IMPLEMENTATION_SUMMARY.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/BUILD_SYSTEM_FIXES_JUNE_2025.md", "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "development/URL_VALIDATION_IMPLEMENTATION.md", "development/../project-management/02_ARCHITECTURE.md", "development/../project-management/03_TECH_SPECS.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/../README.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "development/README.md"], "development/DOCUMENTATION_UPDATE_SUMMARY.md": [], "development/AI_AGENT_TRANSITION_SUMMARY.md": [], "development/ENHANCED_FEATURES_IMPLEMENTATION.md": [], "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": [], "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": [], "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": [], "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": [], "development/PHASE2_IMPLEMENTATION_SUMMARY.md": [], "project-management/05_DATA_POLICY.md": [], "project-management/00_PROJECT_OVERVIEW.md": [], "project-management/01_REQUIREMENTS.md": [], "project-management/GLOSSARY.md": [], "project-management/07_PROJECT_STATUS.md": [], "project-management/02_ARCHITECTURE.md": [], "project-management/README.md": ["project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/ASSESSMENT_SYSTEM.md", "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "project-management/GLOSSARY.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/../README.md", "project-management/README.md"], "project-management/03_TECH_SPECS.md": [], "project-management/04_UX_GUIDELINES.md": [], "project-management/ASSESSMENT_SYSTEM.md": [], "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": [], "project-management/ORGANIZATION_COMPLETE_SUMMARY.md": ["PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "project-management/../DOCUMENTATION_INDEX.md", "DOCUMENTATION_INDEX.md"], "features/MAJOR_DUPLICATION_CLEANUP.md": [], "features/PROGRESS_ANALYTICS_CONSOLIDATION.md": [], "features/README.md": ["features/ENHANCED_ASSESSMENT_RESULTS.md", "features/ASSESSMENT_RESULTS_INTEGRATION.md", "features/../README.md", "features/README.md"], "features/ENHANCED_ASSESSMENT_RESULTS.md": [], "features/ASSESSMENT_RESULTS_INTEGRATION.md": [], "features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md": [], "features/NAVIGATION_CLEANUP.md": [], "workflows/documentation-migration.md": ["atoms/concepts/directory-structure.md", "atoms/concepts/naming-conventions.md", "atoms/setup/environment.md"], "workflows/testing.md": ["atoms/setup/environment.md", "workflows/deployment.md"], "workflows/deployment.md": ["atoms/procedures/deployment-checklist.md", "workflows/testing.md", "workflows/development-setup.md"], "workflows/development-setup.md": ["atoms/setup/environment.md", "atoms/setup/database-setup.md", "atoms/commands/development.md", "workflows/testing.md"], "archives/ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL_20250615.md": [], "archives/DOCUMENTATION_UPDATE_DATABASE_MIGRATION_20250615.md": [], "archives/IMPLEMENTATION_COMPLETE_SUMMARY_20250615.md": [], "archives/DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md": [], "archives/DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY_20250615.md": [], "archives/DOCUMENTATION_UPDATE_JUNE_2025_20250615.md": [], "archives/DOCUMENTATION_ORGANIZATION_SYSTEM_20250615.md": [], "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md": ["README.md", "project-management/README.md", "project-management/03_TECH_SPECS.md", "development/README.md", "user-guides/API.md", "testing/README.md", "operations/README.md", "operations/maintenance.md", "operations/database-backup.md", "user-guides/README.md", "user-guides/faq-troubleshooting.md", "project-management/00_PROJECT_OVERVIEW.md", "user-guides/API.md", "user-guides/user-guide.md", "operations/deployment.md", "user-guides/troubleshooting-guide.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "README.md"], "archives/DOCUMENTATION_UPDATE_ASSESSMENT_TESTING_20250615.md": [], "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md": ["development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "README.md", "development/README.md", "project-management/07_PROJECT_STATUS.md", "DOCUMENTATION_INDEX.md"], "archives/SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL_20250615.md": [], "archives/DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md": [], "archives/UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE_20250615.md": [], "archives/DOCUMENTATION_REORGANIZATION_SUMMARY_20250615.md": [], "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md": ["archives/../README.md", "testing/README.md", "PROJECT_MAP.md", "DOCUMENTATION_INDEX.md", "README.md"], "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md": ["DOCUMENTATION_INDEX.md", "PROJECT_MAP.md", "DOCUMENTATION_INDEX.md"], "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md": ["README.md", "features/README.md", "development/README.md", "testing/README.md"], "operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md": [], "operations/VERCEL_DEPLOYMENT_SUMMARY.md": [], "operations/README.md": ["operations/deployment.md", "operations/database-backup.md", "operations/maintenance.md", "operations/../README.md", "operations/README.md"], "operations/database-backup.md": ["operations/README.md", "operations/../README.md", "operations/README.md"], "operations/deployment.md": ["operations/README.md", "operations/../README.md", "operations/README.md"], "operations/COMPLETE_CLEANUP_GUIDE.md": [], "operations/VERCEL_DEPLOYMENT_GUIDE.md": [], "operations/maintenance.md": ["operations/README.md", "operations/../README.md", "operations/README.md"], "operations/DEPLOYMENT_CHECKLIST.md": [], "user-guides/troubleshooting-guide.md": [], "user-guides/API.md": [], "user-guides/README.md": ["user-guides/user-guide.md", "user-guides/API.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "user-guides/../README.md", "user-guides/README.md"], "user-guides/user-guide.md": [], "user-guides/faq-troubleshooting.md": [], "testing/README.md": ["testing/core/TESTERAT_GUIDE.md", "testing/core/TESTERAT_QUICK_REFERENCE.md", "testing/core/TESTING_GUIDE.md", "testing/core/testing-strategy.md", "testing/core/06_TESTING_FRAMEWORK.md", "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md", "testing/reports/COMPREHENSIVE_TESTING_REPORT.md", "testing/reports/FINAL_TESTING_REPORT.md", "testing/reports/TEST_EXECUTION_SUMMARY.md", "testing/reports/IMPLEMENTATION_TEST_REPORT.md", "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md", "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md", "testing/api-testing/REAL_DATABASE_TESTING.md", "testing/../README.md", "testing/README.md"], "testing/SUPER_TESTERATOR_FIXES_COMPLETE.md": [], "testing/core/TESTING_GUIDE.md": [], "testing/core/06_TESTING_FRAMEWORK.md": [], "testing/core/TESTERAT_GUIDE.md": [], "testing/core/TESTERAT_QUICK_REFERENCE.md": ["README.md", "PROJECT_MAP.md"], "testing/core/testing-strategy.md": [], "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": [], "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": [], "testing/api-testing/REAL_DATABASE_TESTING.md": [], "testing/legacy/DASHBOARD_TEST_REPORT.md": [], "testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md": [], "testing/legacy/ASSESSMENT_TESTING_PLAN.md": [], "testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md": [], "testing/legacy/ASSESSMENT_TESTING_SUMMARY.md": [], "testing/legacy/PROFILE_TESTING_CHECKLIST.md": [], "testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md": [], "testing/reports/FINAL_TESTING_REPORT.md": [], "testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md": [], "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": [], "testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md": [], "testing/reports/IMPLEMENTATION_TEST_REPORT.md": [], "testing/reports/FINAL_TEST_EXECUTION_REPORT.md": [], "testing/reports/TEST_EXECUTION_SUMMARY.md": [], "testing/reports/TESTING_INFRASTRUCTURE_FIXED.md": [], "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": [], "api/FREEDOM_FUND_API_VERIFICATION.md": [], "templates/DOCUMENT_TEMPLATE.md": [], "reference/migration-summary.md": []}, "usage": {"atoms/setup/environment.md": ["STYLE_GUIDE.md", "STYLE_GUIDE.md", "workflows/documentation-migration.md", "workflows/testing.md", "workflows/development-setup.md"], "workflows/testing.md": ["STYLE_GUIDE.md", "workflows/deployment.md", "workflows/development-setup.md"], "PROJECT_STRUCTURE_GUIDE.md": ["README.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "PROJECT_CONVENTIONS.md": ["README.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "project-management/00_PROJECT_OVERVIEW.md": ["README.md", "README.md", "project-management/README.md", "project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/01_REQUIREMENTS.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/02_ARCHITECTURE.md": ["README.md", "README.md", "development/README.md", "project-management/README.md", "project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/03_TECH_SPECS.md": ["README.md", "README.md", "development/README.md", "project-management/README.md", "project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/04_UX_GUIDELINES.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/05_DATA_POLICY.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/ASSESSMENT_SYSTEM.md": ["README.md", "project-management/README.md"], "project-management/GLOSSARY.md": ["README.md", "project-management/README.md"], "development/PHASE2_IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/PHASE3_IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/CODE_QUALITY_FIXES_SUMMARY.md": ["README.md", "development/README.md", "development/README.md"], "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": ["README.md", "development/README.md"], "development/NAVIGATION_ENHANCEMENT_REPORT.md": ["README.md", "development/README.md"], "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": ["README.md", "development/README.md"], "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": ["README.md", "development/README.md"], "development/ENHANCED_FEATURES_IMPLEMENTATION.md": ["README.md", "development/README.md"], "development/IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/AI_AGENT_TRANSITION_SUMMARY.md": ["README.md", "README.md"], "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": ["README.md", "development/README.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md"], "features/ENHANCED_ASSESSMENT_RESULTS.md": ["README.md", "features/README.md"], "features/ASSESSMENT_RESULTS_INTEGRATION.md": ["README.md", "features/README.md"], "user-guides/user-guide.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "user-guides/API.md": ["README.md", "README.md", "README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "user-guides/faq-troubleshooting.md": ["README.md", "README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "user-guides/troubleshooting-guide.md": ["README.md", "README.md", "README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "operations/database-backup.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md"], "operations/deployment.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md"], "operations/maintenance.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md"], "PROJECT_MAP.md": ["DOCUMENTATION_INDEX.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md", "testing/core/TESTERAT_QUICK_REFERENCE.md"], "README.md": ["DOCUMENTATION_INDEX.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md", "testing/core/TESTERAT_QUICK_REFERENCE.md"], "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": ["development/README.md"], "development/URL_VALIDATION_IMPLEMENTATION.md": ["development/README.md"], "development/../project-management/02_ARCHITECTURE.md": ["development/README.md"], "development/../project-management/03_TECH_SPECS.md": ["development/README.md"], "development/../README.md": ["development/README.md"], "development/README.md": ["development/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md"], "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": ["project-management/README.md"], "project-management/../README.md": ["project-management/README.md"], "project-management/README.md": ["project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/../DOCUMENTATION_INDEX.md": ["project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "DOCUMENTATION_INDEX.md": ["project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md"], "features/../README.md": ["features/README.md"], "features/README.md": ["features/README.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md"], "atoms/concepts/directory-structure.md": ["workflows/documentation-migration.md"], "atoms/concepts/naming-conventions.md": ["workflows/documentation-migration.md"], "workflows/deployment.md": ["workflows/testing.md"], "atoms/procedures/deployment-checklist.md": ["workflows/deployment.md"], "workflows/development-setup.md": ["workflows/deployment.md"], "atoms/setup/database-setup.md": ["workflows/development-setup.md"], "atoms/commands/development.md": ["workflows/development-setup.md"], "testing/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md", "testing/README.md"], "operations/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md", "operations/database-backup.md", "operations/database-backup.md", "operations/deployment.md", "operations/deployment.md", "operations/maintenance.md", "operations/maintenance.md"], "user-guides/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "project-management/07_PROJECT_STATUS.md": ["archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md"], "archives/../README.md": ["archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md"], "operations/../README.md": ["operations/README.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md"], "user-guides/../README.md": ["user-guides/README.md"], "testing/core/TESTERAT_GUIDE.md": ["testing/README.md"], "testing/core/TESTERAT_QUICK_REFERENCE.md": ["testing/README.md"], "testing/core/TESTING_GUIDE.md": ["testing/README.md"], "testing/core/testing-strategy.md": ["testing/README.md"], "testing/core/06_TESTING_FRAMEWORK.md": ["testing/README.md"], "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": ["testing/README.md"], "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": ["testing/README.md"], "testing/reports/FINAL_TESTING_REPORT.md": ["testing/README.md"], "testing/reports/TEST_EXECUTION_SUMMARY.md": ["testing/README.md"], "testing/reports/IMPLEMENTATION_TEST_REPORT.md": ["testing/README.md"], "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": ["testing/README.md"], "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": ["testing/README.md"], "testing/api-testing/REAL_DATABASE_TESTING.md": ["testing/README.md"], "testing/../README.md": ["testing/README.md"]}, "metadata": {"PROJECT_MAP.md": {}, "PROJECT_CONVENTIONS.md": {}, "PROJECT_NAVIGATION_SYSTEM.md": {}, "STYLE_GUIDE.md": {}, "README.md": {}, "PROJECT_STRUCTURE_GUIDE.md": {}, "DOCUMENTATION_INDEX.md": {}, "resource-improvement-summary.md": {}, "atoms/procedures/deployment-checklist.md": {"title": "Deployment Checklist", "category": "atoms", "subcategory": "procedures", "tags": ["deployment", "checklist", "production", "verification"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "operations-team", "ai_context": "Pre-deployment checklist for FAAFO Career Platform"}, "atoms/procedures/url-validation.md": {"title": "URL Validation Procedures", "category": "atoms", "subcategory": "procedures", "tags": ["url", "validation", "resources", "quality-assurance"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "quality-team", "ai_context": "URL validation and resource quality procedures for FAAFO Career Platform"}, "atoms/setup/environment.md": {"title": "Environment Setup", "category": "atoms", "subcategory": "setup", "tags": ["environment", "setup", "development", "prerequisites"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "development-team", "ai_context": "Essential environment setup steps for FAAFO Career Platform development"}, "atoms/setup/database-setup.md": {"title": "Database Setup", "category": "atoms", "subcategory": "setup", "tags": ["database", "setup", "prisma", "postgresql", "sqlite"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": ["environment.md"], "used_by": [], "maintainer": "development-team", "ai_context": "Database setup procedures for FAAFO Career Platform"}, "atoms/concepts/validation-system.md": {"title": "Validation System Architecture", "category": "atoms", "subcategory": "concepts", "tags": ["validation", "zod", "security", "input-handling"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "security-team", "ai_context": "Input validation and security architecture for FAAFO Career Platform"}, "atoms/concepts/directory-structure.md": {"title": "Documentation Directory Structure", "category": "atoms", "subcategory": "concepts", "tags": ["structure", "organization", "directories", "docs"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "documentation-team", "ai_context": "Standard directory structure for FAAFO Career Platform documentation"}, "atoms/concepts/naming-conventions.md": {"title": "Documentation Naming Conventions", "category": "atoms", "subcategory": "concepts", "tags": ["naming", "conventions", "files", "standards"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "documentation-team", "ai_context": "File naming conventions for FAAFO Career Platform documentation"}, "atoms/concepts/testing-structure.md": {"title": "Testing Documentation Structure", "category": "atoms", "subcategory": "concepts", "tags": ["testing", "structure", "organization", "testerat"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "testing-team", "ai_context": "Testing documentation organization structure for FAAFO Career Platform"}, "atoms/commands/testing.md": {"title": "Testing Commands", "category": "atoms", "subcategory": "commands", "tags": ["testing", "commands", "jest", "playwright"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "testing-team", "ai_context": "Standard testing commands for FAAFO Career Platform"}, "atoms/commands/testerat.md": {"title": "Testerat Commands", "category": "atoms", "subcategory": "commands", "tags": ["testerat", "testing", "commands", "ai-testing"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "testing-team", "ai_context": "Testerat AI testing tool commands for FAAFO Career Platform"}, "atoms/commands/development.md": {"title": "Development Commands", "category": "atoms", "subcategory": "commands", "tags": ["development", "commands", "npm", "scripts"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "development-team", "ai_context": "Standard development commands for FAAFO Career Platform"}, "development/PHASE3_IMPLEMENTATION_SUMMARY.md": {}, "development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md": {}, "development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md": {}, "development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md": {}, "development/CODE_QUALITY_FIXES_SUMMARY.md": {}, "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": {}, "development/URL_VALIDATION_IMPLEMENTATION.md": {}, "development/PHASE1_IMPLEMENTATION_PLAN.md": {}, "development/IMPLEMENTATION_SUMMARY.md": {}, "development/IMPLEMENTATION_COMPLETE.md": {}, "development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md": {}, "development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md": {}, "development/PHASE1_SETUP_GUIDE.md": {}, "development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md": {}, "development/PHASE1_IMPLEMENTATION_COMPLETE.md": {}, "development/NAVIGATION_ENHANCEMENT_REPORT.md": {}, "development/README.md": {}, "development/DOCUMENTATION_UPDATE_SUMMARY.md": {}, "development/AI_AGENT_TRANSITION_SUMMARY.md": {}, "development/ENHANCED_FEATURES_IMPLEMENTATION.md": {}, "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": {}, "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": {}, "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": {}, "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": {}, "development/PHASE2_IMPLEMENTATION_SUMMARY.md": {}, "project-management/05_DATA_POLICY.md": {}, "project-management/00_PROJECT_OVERVIEW.md": {}, "project-management/01_REQUIREMENTS.md": {}, "project-management/GLOSSARY.md": {}, "project-management/07_PROJECT_STATUS.md": {}, "project-management/02_ARCHITECTURE.md": {}, "project-management/README.md": {}, "project-management/03_TECH_SPECS.md": {}, "project-management/04_UX_GUIDELINES.md": {}, "project-management/ASSESSMENT_SYSTEM.md": {}, "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": {}, "project-management/ORGANIZATION_COMPLETE_SUMMARY.md": {}, "features/MAJOR_DUPLICATION_CLEANUP.md": {}, "features/PROGRESS_ANALYTICS_CONSOLIDATION.md": {}, "features/README.md": {}, "features/ENHANCED_ASSESSMENT_RESULTS.md": {}, "features/ASSESSMENT_RESULTS_INTEGRATION.md": {}, "features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md": {}, "features/NAVIGATION_CLEANUP.md": {}, "workflows/documentation-migration.md": {"title": "Documentation Migration Workflow", "category": "workflows", "tags": ["migration", "documentation", "atomic-design", "cleanup"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": ["atoms/concepts/directory-structure.md", "atoms/concepts/naming-conventions.md"], "used_by": [], "maintainer": "documentation-team", "ai_context": "Systematic workflow for migrating legacy documentation to atomic design system"}, "workflows/testing.md": {"title": "Complete Testing Workflow", "category": "workflows", "tags": ["testing", "workflow", "quality-assurance", "ci-cd"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": ["atoms/setup/environment.md", "atoms/commands/testing.md"], "used_by": [], "maintainer": "testing-team", "ai_context": "Complete end-to-end testing workflow for FAAFO Career Platform"}, "workflows/deployment.md": {"title": "Complete Deployment Workflow", "category": "workflows", "tags": ["deployment", "production", "workflow", "operations"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": ["atoms/procedures/deployment-checklist.md", "atoms/commands/testing.md"], "used_by": [], "maintainer": "operations-team", "ai_context": "Complete production deployment workflow for FAAFO Career Platform"}, "workflows/development-setup.md": {"title": "Complete Development Setup Workflow", "category": "workflows", "tags": ["development", "setup", "workflow", "onboarding"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": ["atoms/setup/environment.md", "atoms/setup/database-setup.md", "atoms/commands/development.md"], "used_by": [], "maintainer": "development-team", "ai_context": "Complete development environment setup workflow for FAAFO Career Platform"}, "archives/ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL_20250615.md": {}, "archives/DOCUMENTATION_UPDATE_DATABASE_MIGRATION_20250615.md": {}, "archives/IMPLEMENTATION_COMPLETE_SUMMARY_20250615.md": {}, "archives/DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md": {}, "archives/DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY_20250615.md": {}, "archives/DOCUMENTATION_UPDATE_JUNE_2025_20250615.md": {}, "archives/DOCUMENTATION_ORGANIZATION_SYSTEM_20250615.md": {}, "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md": {}, "archives/DOCUMENTATION_UPDATE_ASSESSMENT_TESTING_20250615.md": {}, "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md": {}, "archives/SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL_20250615.md": {}, "archives/DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md": {}, "archives/UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE_20250615.md": {}, "archives/DOCUMENTATION_REORGANIZATION_SUMMARY_20250615.md": {}, "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md": {}, "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md": {}, "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md": {}, "operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md": {}, "operations/VERCEL_DEPLOYMENT_SUMMARY.md": {}, "operations/README.md": {}, "operations/database-backup.md": {}, "operations/deployment.md": {}, "operations/COMPLETE_CLEANUP_GUIDE.md": {}, "operations/VERCEL_DEPLOYMENT_GUIDE.md": {}, "operations/maintenance.md": {}, "operations/DEPLOYMENT_CHECKLIST.md": {}, "user-guides/troubleshooting-guide.md": {}, "user-guides/API.md": {}, "user-guides/README.md": {}, "user-guides/user-guide.md": {}, "user-guides/faq-troubleshooting.md": {}, "testing/README.md": {}, "testing/SUPER_TESTERATOR_FIXES_COMPLETE.md": {}, "testing/core/TESTING_GUIDE.md": {}, "testing/core/06_TESTING_FRAMEWORK.md": {}, "testing/core/TESTERAT_GUIDE.md": {}, "testing/core/TESTERAT_QUICK_REFERENCE.md": {}, "testing/core/testing-strategy.md": {}, "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": {}, "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": {}, "testing/api-testing/REAL_DATABASE_TESTING.md": {}, "testing/legacy/DASHBOARD_TEST_REPORT.md": {}, "testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md": {}, "testing/legacy/ASSESSMENT_TESTING_PLAN.md": {}, "testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md": {}, "testing/legacy/ASSESSMENT_TESTING_SUMMARY.md": {}, "testing/legacy/PROFILE_TESTING_CHECKLIST.md": {}, "testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md": {}, "testing/reports/FINAL_TESTING_REPORT.md": {}, "testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md": {}, "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": {}, "testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md": {}, "testing/reports/IMPLEMENTATION_TEST_REPORT.md": {}, "testing/reports/FINAL_TEST_EXECUTION_REPORT.md": {}, "testing/reports/TEST_EXECUTION_SUMMARY.md": {}, "testing/reports/TESTING_INFRASTRUCTURE_FIXED.md": {}, "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": {}, "api/FREEDOM_FUND_API_VERIFICATION.md": {}, "templates/DOCUMENT_TEMPLATE.md": {}, "reference/migration-summary.md": {"title": "Documentation Migration Summary", "category": "reference", "tags": ["migration", "atomic-design", "cleanup"], "last_updated": "2025-06-15", "generated_date": "2025-06-15", "generator": "migrate-legacy-docs.py", "ai_context": "Summary of documentation migration to atomic design system"}}, "metrics": {"total_files": 127, "files_with_dependencies": 23, "total_dependencies": 175, "orphaned_files": ["archives/DOCUMENTATION_UPDATE_JUNE_2025_20250615.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md", "testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md", "workflows/documentation-migration.md", "features/MAJOR_DUPLICATION_CLEANUP.md", "archives/DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md", "atoms/procedures/url-validation.md", "development/IMPLEMENTATION_COMPLETE.md", "STYLE_GUIDE.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md", "testing/legacy/ASSESSMENT_TESTING_SUMMARY.md", "testing/SUPER_TESTERATOR_FIXES_COMPLETE.md", "testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md", "archives/DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/DEPLOYMENT_CHECKLIST.md", "operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md", "development/PHASE1_IMPLEMENTATION_PLAN.md", "features/NAVIGATION_CLEANUP.md", "development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md", "development/DOCUMENTATION_UPDATE_SUMMARY.md", "operations/VERCEL_DEPLOYMENT_GUIDE.md", "templates/DOCUMENT_TEMPLATE.md", "archives/SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL_20250615.md", "development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md", "atoms/concepts/validation-system.md", "atoms/commands/testerat.md", "testing/reports/FINAL_TEST_EXECUTION_REPORT.md", "archives/DOCUMENTATION_ORGANIZATION_SYSTEM_20250615.md", "operations/VERCEL_DEPLOYMENT_SUMMARY.md", "archives/DOCUMENTATION_UPDATE_DATABASE_MIGRATION_20250615.md", "api/FREEDOM_FUND_API_VERIFICATION.md", "testing/legacy/PROFILE_TESTING_CHECKLIST.md", "testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md", "testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md", "features/PROGRESS_ANALYTICS_CONSOLIDATION.md", "development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md", "archives/ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL_20250615.md", "development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md", "development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md", "reference/migration-summary.md", "archives/UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE_20250615.md", "archives/DOCUMENTATION_UPDATE_ASSESSMENT_TESTING_20250615.md", "operations/COMPLETE_CLEANUP_GUIDE.md", "archives/DOCUMENTATION_REORGANIZATION_SUMMARY_20250615.md", "PROJECT_NAVIGATION_SYSTEM.md", "development/PHASE1_IMPLEMENTATION_COMPLETE.md", "development/PHASE1_SETUP_GUIDE.md", "testing/legacy/ASSESSMENT_TESTING_PLAN.md", "resource-improvement-summary.md", "atoms/commands/testing.md", "testing/reports/TESTING_INFRASTRUCTURE_FIXED.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md", "archives/IMPLEMENTATION_COMPLETE_SUMMARY_20250615.md", "features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md", "testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md", "testing/legacy/DASHBOARD_TEST_REPORT.md", "atoms/concepts/testing-structure.md"], "most_used_files": [["operations/README.md", 8], ["project-management/03_TECH_SPECS.md", 7], ["README.md", 7], ["project-management/02_ARCHITECTURE.md", 6], ["user-guides/API.md", 6], ["atoms/setup/environment.md", 5], ["project-management/00_PROJECT_OVERVIEW.md", 5], ["user-guides/troubleshooting-guide.md", 5], ["DOCUMENTATION_INDEX.md", 5], ["user-guides/faq-troubleshooting.md", 4]], "most_dependent_files": [["README.md", 39], ["development/README.md", 19], ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", 19], ["project-management/README.md", 17], ["testing/README.md", 15], ["project-management/ORGANIZATION_COMPLETE_SUMMARY.md", 6], ["user-guides/README.md", 6], ["archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", 5], ["archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", 5], ["operations/README.md", 5]], "circular_dependencies": [], "categories": {"unknown": 111, "atoms": 11, "workflows": 4, "reference": 1}, "freshness": {"stale_files": []}}}