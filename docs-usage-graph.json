{"generated_at": "2025-06-15T22:17:51.005728", "dependencies": {"PROJECT_MAP.md": [], "PROJECT_CONVENTIONS.md": [], "DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md": [], "PROJECT_NAVIGATION_SYSTEM.md": [], "DOCUMENTATION_MIGRATION_GUIDE.md": ["README.md", "project-management/README.md", "project-management/03_TECH_SPECS.md", "development/README.md", "user-guides/API.md", "testing/README.md", "operations/README.md", "operations/maintenance.md", "operations/database-backup.md", "user-guides/README.md", "user-guides/faq-troubleshooting.md", "project-management/00_PROJECT_OVERVIEW.md", "user-guides/API.md", "user-guides/user-guide.md", "operations/deployment.md", "user-guides/troubleshooting-guide.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "README.md"], "DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY.md": [], "SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL.md": [], "STYLE_GUIDE.md": ["atoms/setup/environment.md", "workflows/testing.md", "atoms/setup/environment.md"], "DOCUMENTATION_ORGANIZATION_SUMMARY.md": ["README.md", "features/README.md", "development/README.md", "testing/README.md"], "IMPLEMENTATION_COMPLETE_SUMMARY.md": [], "UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE.md": [], "README.md": ["DOCUMENTATION_ORGANIZATION_SUMMARY.md", "DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md", "PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/ASSESSMENT_SYSTEM.md", "project-management/GLOSSARY.md", "development/PHASE2_IMPLEMENTATION_SUMMARY.md", "development/PHASE3_IMPLEMENTATION_SUMMARY.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md", "development/NAVIGATION_ENHANCEMENT_REPORT.md", "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md", "development/ENHANCED_FEATURES_IMPLEMENTATION.md", "development/IMPLEMENTATION_SUMMARY.md", "development/AI_AGENT_TRANSITION_SUMMARY.md", "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "features/ENHANCED_ASSESSMENT_RESULTS.md", "features/ASSESSMENT_RESULTS_INTEGRATION.md", "user-guides/user-guide.md", "user-guides/API.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/03_TECH_SPECS.md", "project-management/02_ARCHITECTURE.md", "user-guides/API.md", "user-guides/troubleshooting-guide.md", "development/AI_AGENT_TRANSITION_SUMMARY.md", "DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md", "DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "user-guides/API.md"], "DOCUMENTATION_REORGANIZATION_SUMMARY.md": [], "DOCUMENTATION_UPDATE_JUNE_2025.md": [], "UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md": ["DOCUMENTATION_INDEX.md", "PROJECT_MAP.md", "DOCUMENTATION_INDEX.md"], "DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md": [], "PROJECT_STRUCTURE_GUIDE.md": [], "DOCUMENTATION_INDEX.md": ["PROJECT_MAP.md", "README.md"], "ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL.md": [], "DOCUMENTATION_ORGANIZATION_SYSTEM.md": [], "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md": ["../README.md", "testing/README.md", "PROJECT_MAP.md", "DOCUMENTATION_INDEX.md", "README.md"], "DOCUMENTATION_UPDATE_ASSESSMENT_TESTING.md": [], "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md": ["development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "README.md", "development/README.md", "project-management/07_PROJECT_STATUS.md", "DOCUMENTATION_INDEX.md"], "resource-improvement-summary.md": [], "DOCUMENTATION_UPDATE_DATABASE_MIGRATION.md": [], "atoms/setup/environment.md": [], "atoms/commands/testing.md": [], "development/PHASE3_IMPLEMENTATION_SUMMARY.md": [], "development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md": [], "development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md": [], "development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md": [], "development/CODE_QUALITY_FIXES_SUMMARY.md": [], "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": [], "development/URL_VALIDATION_IMPLEMENTATION.md": [], "development/PHASE1_IMPLEMENTATION_PLAN.md": [], "development/IMPLEMENTATION_SUMMARY.md": [], "development/IMPLEMENTATION_COMPLETE.md": [], "development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md": [], "development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md": [], "development/PHASE1_SETUP_GUIDE.md": [], "development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md": [], "development/PHASE1_IMPLEMENTATION_COMPLETE.md": [], "development/NAVIGATION_ENHANCEMENT_REPORT.md": [], "development/README.md": ["development/PHASE2_IMPLEMENTATION_SUMMARY.md", "development/PHASE3_IMPLEMENTATION_SUMMARY.md", "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md", "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md", "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "development/NAVIGATION_ENHANCEMENT_REPORT.md", "development/ENHANCED_FEATURES_IMPLEMENTATION.md", "development/IMPLEMENTATION_SUMMARY.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/BUILD_SYSTEM_FIXES_JUNE_2025.md", "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "development/URL_VALIDATION_IMPLEMENTATION.md", "development/../project-management/02_ARCHITECTURE.md", "development/../project-management/03_TECH_SPECS.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/../README.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "development/README.md"], "development/DOCUMENTATION_UPDATE_SUMMARY.md": [], "development/AI_AGENT_TRANSITION_SUMMARY.md": [], "development/ENHANCED_FEATURES_IMPLEMENTATION.md": [], "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": [], "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": [], "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": [], "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": [], "development/PHASE2_IMPLEMENTATION_SUMMARY.md": [], "project-management/05_DATA_POLICY.md": [], "project-management/00_PROJECT_OVERVIEW.md": [], "project-management/01_REQUIREMENTS.md": [], "project-management/GLOSSARY.md": [], "project-management/07_PROJECT_STATUS.md": [], "project-management/02_ARCHITECTURE.md": [], "project-management/README.md": ["project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/ASSESSMENT_SYSTEM.md", "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "project-management/GLOSSARY.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/../README.md", "project-management/README.md"], "project-management/03_TECH_SPECS.md": [], "project-management/04_UX_GUIDELINES.md": [], "project-management/ASSESSMENT_SYSTEM.md": [], "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": [], "project-management/ORGANIZATION_COMPLETE_SUMMARY.md": ["PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "project-management/../DOCUMENTATION_INDEX.md", "DOCUMENTATION_INDEX.md"], "features/MAJOR_DUPLICATION_CLEANUP.md": [], "features/PROGRESS_ANALYTICS_CONSOLIDATION.md": [], "features/README.md": ["features/ENHANCED_ASSESSMENT_RESULTS.md", "features/ASSESSMENT_RESULTS_INTEGRATION.md", "features/../README.md", "features/README.md"], "features/ENHANCED_ASSESSMENT_RESULTS.md": [], "features/ASSESSMENT_RESULTS_INTEGRATION.md": [], "features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md": [], "features/NAVIGATION_CLEANUP.md": [], "workflows/testing.md": ["atoms/setup/environment.md"], "operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md": [], "operations/VERCEL_DEPLOYMENT_SUMMARY.md": [], "operations/README.md": ["operations/deployment.md", "operations/database-backup.md", "operations/maintenance.md", "operations/../README.md", "operations/README.md"], "operations/database-backup.md": ["operations/README.md", "operations/../README.md", "operations/README.md"], "operations/deployment.md": ["operations/README.md", "operations/../README.md", "operations/README.md"], "operations/COMPLETE_CLEANUP_GUIDE.md": [], "operations/VERCEL_DEPLOYMENT_GUIDE.md": [], "operations/maintenance.md": ["operations/README.md", "operations/../README.md", "operations/README.md"], "operations/DEPLOYMENT_CHECKLIST.md": [], "user-guides/troubleshooting-guide.md": [], "user-guides/API.md": [], "user-guides/README.md": ["user-guides/user-guide.md", "user-guides/API.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "user-guides/../README.md", "user-guides/README.md"], "user-guides/user-guide.md": [], "user-guides/faq-troubleshooting.md": [], "testing/README.md": ["testing/core/TESTERAT_GUIDE.md", "testing/core/TESTERAT_QUICK_REFERENCE.md", "testing/core/TESTING_GUIDE.md", "testing/core/testing-strategy.md", "testing/core/06_TESTING_FRAMEWORK.md", "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md", "testing/reports/COMPREHENSIVE_TESTING_REPORT.md", "testing/reports/FINAL_TESTING_REPORT.md", "testing/reports/TEST_EXECUTION_SUMMARY.md", "testing/reports/IMPLEMENTATION_TEST_REPORT.md", "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md", "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md", "testing/api-testing/REAL_DATABASE_TESTING.md", "testing/../README.md", "testing/README.md"], "testing/SUPER_TESTERATOR_FIXES_COMPLETE.md": [], "testing/core/TESTING_GUIDE.md": [], "testing/core/06_TESTING_FRAMEWORK.md": [], "testing/core/TESTERAT_GUIDE.md": [], "testing/core/TESTERAT_QUICK_REFERENCE.md": ["README.md", "PROJECT_MAP.md"], "testing/core/testing-strategy.md": [], "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": [], "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": [], "testing/api-testing/REAL_DATABASE_TESTING.md": [], "testing/legacy/DASHBOARD_TEST_REPORT.md": [], "testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md": [], "testing/legacy/ASSESSMENT_TESTING_PLAN.md": [], "testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md": [], "testing/legacy/ASSESSMENT_TESTING_SUMMARY.md": [], "testing/legacy/PROFILE_TESTING_CHECKLIST.md": [], "testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md": [], "testing/reports/FINAL_TESTING_REPORT.md": [], "testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md": [], "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": [], "testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md": [], "testing/reports/IMPLEMENTATION_TEST_REPORT.md": [], "testing/reports/FINAL_TEST_EXECUTION_REPORT.md": [], "testing/reports/TEST_EXECUTION_SUMMARY.md": [], "testing/reports/TESTING_INFRASTRUCTURE_FIXED.md": [], "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": [], "api/FREEDOM_FUND_API_VERIFICATION.md": [], "templates/DOCUMENT_TEMPLATE.md": []}, "usage": {"README.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "DOCUMENTATION_MIGRATION_GUIDE.md", "DOCUMENTATION_ORGANIZATION_SUMMARY.md", "DOCUMENTATION_INDEX.md", "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md", "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md", "testing/core/TESTERAT_QUICK_REFERENCE.md"], "project-management/README.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "project-management/README.md"], "project-management/03_TECH_SPECS.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "README.md", "development/README.md", "project-management/README.md", "project-management/README.md"], "development/README.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "DOCUMENTATION_ORGANIZATION_SUMMARY.md", "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md", "development/README.md"], "user-guides/API.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "README.md", "README.md", "user-guides/README.md"], "testing/README.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "DOCUMENTATION_ORGANIZATION_SUMMARY.md", "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md", "testing/README.md"], "operations/README.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "operations/README.md", "operations/database-backup.md", "operations/database-backup.md", "operations/deployment.md", "operations/deployment.md", "operations/maintenance.md", "operations/maintenance.md"], "operations/maintenance.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "operations/README.md"], "operations/database-backup.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "operations/README.md"], "user-guides/README.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "user-guides/README.md"], "user-guides/faq-troubleshooting.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "README.md", "user-guides/README.md"], "project-management/00_PROJECT_OVERVIEW.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "README.md", "project-management/README.md", "project-management/README.md"], "user-guides/user-guide.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "user-guides/README.md"], "operations/deployment.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "operations/README.md"], "user-guides/troubleshooting-guide.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "README.md", "README.md", "user-guides/README.md"], "project-management/02_ARCHITECTURE.md": ["DOCUMENTATION_MIGRATION_GUIDE.md", "README.md", "README.md", "development/README.md", "project-management/README.md", "project-management/README.md"], "atoms/setup/environment.md": ["STYLE_GUIDE.md", "STYLE_GUIDE.md", "workflows/testing.md"], "workflows/testing.md": ["STYLE_GUIDE.md"], "features/README.md": ["DOCUMENTATION_ORGANIZATION_SUMMARY.md", "features/README.md"], "DOCUMENTATION_ORGANIZATION_SUMMARY.md": ["README.md"], "DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md": ["README.md", "README.md", "README.md"], "PROJECT_STRUCTURE_GUIDE.md": ["README.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "PROJECT_CONVENTIONS.md": ["README.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "project-management/01_REQUIREMENTS.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/04_UX_GUIDELINES.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/05_DATA_POLICY.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/ASSESSMENT_SYSTEM.md": ["README.md", "project-management/README.md"], "project-management/GLOSSARY.md": ["README.md", "project-management/README.md"], "development/PHASE2_IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/PHASE3_IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/CODE_QUALITY_FIXES_SUMMARY.md": ["README.md", "development/README.md", "development/README.md"], "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": ["README.md", "development/README.md"], "development/NAVIGATION_ENHANCEMENT_REPORT.md": ["README.md", "development/README.md"], "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": ["README.md", "development/README.md"], "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": ["README.md", "development/README.md"], "development/ENHANCED_FEATURES_IMPLEMENTATION.md": ["README.md", "development/README.md"], "development/IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/AI_AGENT_TRANSITION_SUMMARY.md": ["README.md", "README.md"], "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": ["README.md", "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md", "development/README.md"], "features/ENHANCED_ASSESSMENT_RESULTS.md": ["README.md", "features/README.md"], "features/ASSESSMENT_RESULTS_INTEGRATION.md": ["README.md", "features/README.md"], "DOCUMENTATION_INDEX.md": ["UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md", "UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md", "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md", "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "PROJECT_MAP.md": ["UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md", "DOCUMENTATION_INDEX.md", "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md", "testing/core/TESTERAT_QUICK_REFERENCE.md"], "../README.md": ["DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md"], "project-management/07_PROJECT_STATUS.md": ["DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md"], "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": ["development/README.md"], "development/URL_VALIDATION_IMPLEMENTATION.md": ["development/README.md"], "development/../project-management/02_ARCHITECTURE.md": ["development/README.md"], "development/../project-management/03_TECH_SPECS.md": ["development/README.md"], "development/../README.md": ["development/README.md"], "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": ["project-management/README.md"], "project-management/../README.md": ["project-management/README.md"], "project-management/../DOCUMENTATION_INDEX.md": ["project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "features/../README.md": ["features/README.md"], "operations/../README.md": ["operations/README.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md"], "user-guides/../README.md": ["user-guides/README.md"], "testing/core/TESTERAT_GUIDE.md": ["testing/README.md"], "testing/core/TESTERAT_QUICK_REFERENCE.md": ["testing/README.md"], "testing/core/TESTING_GUIDE.md": ["testing/README.md"], "testing/core/testing-strategy.md": ["testing/README.md"], "testing/core/06_TESTING_FRAMEWORK.md": ["testing/README.md"], "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": ["testing/README.md"], "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": ["testing/README.md"], "testing/reports/FINAL_TESTING_REPORT.md": ["testing/README.md"], "testing/reports/TEST_EXECUTION_SUMMARY.md": ["testing/README.md"], "testing/reports/IMPLEMENTATION_TEST_REPORT.md": ["testing/README.md"], "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": ["testing/README.md"], "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": ["testing/README.md"], "testing/api-testing/REAL_DATABASE_TESTING.md": ["testing/README.md"], "testing/../README.md": ["testing/README.md"]}, "metadata": {"PROJECT_MAP.md": {}, "PROJECT_CONVENTIONS.md": {}, "DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md": {}, "PROJECT_NAVIGATION_SYSTEM.md": {}, "DOCUMENTATION_MIGRATION_GUIDE.md": {}, "DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY.md": {}, "SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL.md": {}, "STYLE_GUIDE.md": {}, "DOCUMENTATION_ORGANIZATION_SUMMARY.md": {}, "IMPLEMENTATION_COMPLETE_SUMMARY.md": {}, "UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE.md": {}, "README.md": {}, "DOCUMENTATION_REORGANIZATION_SUMMARY.md": {}, "DOCUMENTATION_UPDATE_JUNE_2025.md": {}, "UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md": {}, "DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md": {}, "PROJECT_STRUCTURE_GUIDE.md": {}, "DOCUMENTATION_INDEX.md": {}, "ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL.md": {}, "DOCUMENTATION_ORGANIZATION_SYSTEM.md": {}, "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md": {}, "DOCUMENTATION_UPDATE_ASSESSMENT_TESTING.md": {}, "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md": {}, "resource-improvement-summary.md": {}, "DOCUMENTATION_UPDATE_DATABASE_MIGRATION.md": {}, "atoms/setup/environment.md": {"title": "Environment Setup", "category": "atoms", "subcategory": "setup", "tags": ["environment", "setup", "development", "prerequisites"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "development-team", "ai_context": "Essential environment setup steps for FAAFO Career Platform development"}, "atoms/commands/testing.md": {"title": "Testing Commands", "category": "atoms", "subcategory": "commands", "tags": ["testing", "commands", "jest", "playwright"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": [], "used_by": [], "maintainer": "testing-team", "ai_context": "Standard testing commands for FAAFO Career Platform"}, "development/PHASE3_IMPLEMENTATION_SUMMARY.md": {}, "development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md": {}, "development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md": {}, "development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md": {}, "development/CODE_QUALITY_FIXES_SUMMARY.md": {}, "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": {}, "development/URL_VALIDATION_IMPLEMENTATION.md": {}, "development/PHASE1_IMPLEMENTATION_PLAN.md": {}, "development/IMPLEMENTATION_SUMMARY.md": {}, "development/IMPLEMENTATION_COMPLETE.md": {}, "development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md": {}, "development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md": {}, "development/PHASE1_SETUP_GUIDE.md": {}, "development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md": {}, "development/PHASE1_IMPLEMENTATION_COMPLETE.md": {}, "development/NAVIGATION_ENHANCEMENT_REPORT.md": {}, "development/README.md": {}, "development/DOCUMENTATION_UPDATE_SUMMARY.md": {}, "development/AI_AGENT_TRANSITION_SUMMARY.md": {}, "development/ENHANCED_FEATURES_IMPLEMENTATION.md": {}, "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": {}, "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": {}, "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": {}, "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": {}, "development/PHASE2_IMPLEMENTATION_SUMMARY.md": {}, "project-management/05_DATA_POLICY.md": {}, "project-management/00_PROJECT_OVERVIEW.md": {}, "project-management/01_REQUIREMENTS.md": {}, "project-management/GLOSSARY.md": {}, "project-management/07_PROJECT_STATUS.md": {}, "project-management/02_ARCHITECTURE.md": {}, "project-management/README.md": {}, "project-management/03_TECH_SPECS.md": {}, "project-management/04_UX_GUIDELINES.md": {}, "project-management/ASSESSMENT_SYSTEM.md": {}, "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": {}, "project-management/ORGANIZATION_COMPLETE_SUMMARY.md": {}, "features/MAJOR_DUPLICATION_CLEANUP.md": {}, "features/PROGRESS_ANALYTICS_CONSOLIDATION.md": {}, "features/README.md": {}, "features/ENHANCED_ASSESSMENT_RESULTS.md": {}, "features/ASSESSMENT_RESULTS_INTEGRATION.md": {}, "features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md": {}, "features/NAVIGATION_CLEANUP.md": {}, "workflows/testing.md": {"title": "Complete Testing Workflow", "category": "workflows", "tags": ["testing", "workflow", "quality-assurance", "ci-cd"], "last_updated": "2025-06-15", "last_validated": "2025-06-15", "dependencies": ["atoms/setup/environment.md", "atoms/commands/testing.md"], "used_by": [], "maintainer": "testing-team", "ai_context": "Complete end-to-end testing workflow for FAAFO Career Platform"}, "operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md": {}, "operations/VERCEL_DEPLOYMENT_SUMMARY.md": {}, "operations/README.md": {}, "operations/database-backup.md": {}, "operations/deployment.md": {}, "operations/COMPLETE_CLEANUP_GUIDE.md": {}, "operations/VERCEL_DEPLOYMENT_GUIDE.md": {}, "operations/maintenance.md": {}, "operations/DEPLOYMENT_CHECKLIST.md": {}, "user-guides/troubleshooting-guide.md": {}, "user-guides/API.md": {}, "user-guides/README.md": {}, "user-guides/user-guide.md": {}, "user-guides/faq-troubleshooting.md": {}, "testing/README.md": {}, "testing/SUPER_TESTERATOR_FIXES_COMPLETE.md": {}, "testing/core/TESTING_GUIDE.md": {}, "testing/core/06_TESTING_FRAMEWORK.md": {}, "testing/core/TESTERAT_GUIDE.md": {}, "testing/core/TESTERAT_QUICK_REFERENCE.md": {}, "testing/core/testing-strategy.md": {}, "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": {}, "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": {}, "testing/api-testing/REAL_DATABASE_TESTING.md": {}, "testing/legacy/DASHBOARD_TEST_REPORT.md": {}, "testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md": {}, "testing/legacy/ASSESSMENT_TESTING_PLAN.md": {}, "testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md": {}, "testing/legacy/ASSESSMENT_TESTING_SUMMARY.md": {}, "testing/legacy/PROFILE_TESTING_CHECKLIST.md": {}, "testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md": {}, "testing/reports/FINAL_TESTING_REPORT.md": {}, "testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md": {}, "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": {}, "testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md": {}, "testing/reports/IMPLEMENTATION_TEST_REPORT.md": {}, "testing/reports/FINAL_TEST_EXECUTION_REPORT.md": {}, "testing/reports/TEST_EXECUTION_SUMMARY.md": {}, "testing/reports/TESTING_INFRASTRUCTURE_FIXED.md": {}, "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": {}, "api/FREEDOM_FUND_API_VERIFICATION.md": {}, "templates/DOCUMENT_TEMPLATE.md": {}}, "metrics": {"total_files": 114, "files_with_dependencies": 20, "total_dependencies": 168, "orphaned_files": ["testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md", "testing/legacy/PROFILE_TESTING_CHECKLIST.md", "testing/reports/TESTING_INFRASTRUCTURE_FIXED.md", "resource-improvement-summary.md", "DOCUMENTATION_UPDATE_ASSESSMENT_TESTING.md", "PROJECT_NAVIGATION_SYSTEM.md", "development/PHASE1_IMPLEMENTATION_COMPLETE.md", "DOCUMENTATION_REORGANIZATION_SUMMARY.md", "testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md", "development/PHASE1_IMPLEMENTATION_PLAN.md", "DOCUMENTATION_ORGANIZATION_SYSTEM.md", "development/IMPLEMENTATION_COMPLETE.md", "features/MAJOR_DUPLICATION_CLEANUP.md", "testing/legacy/ASSESSMENT_TESTING_SUMMARY.md", "features/PROGRESS_ANALYTICS_CONSOLIDATION.md", "api/FREEDOM_FUND_API_VERIFICATION.md", "ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL.md", "development/PHASE1_SETUP_GUIDE.md", "development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md", "operations/COMPLETE_CLEANUP_GUIDE.md", "development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md", "DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md", "features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md", "testing/reports/FINAL_TEST_EXECUTION_REPORT.md", "development/DOCUMENTATION_UPDATE_SUMMARY.md", "development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md", "testing/legacy/ASSESSMENT_TESTING_PLAN.md", "DOCUMENTATION_MIGRATION_GUIDE.md", "UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE.md", "UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md", "operations/DEPLOYMENT_CHECKLIST.md", "testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md", "DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md", "STYLE_GUIDE.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md", "DOCUMENTATION_UPDATE_DATABASE_MIGRATION.md", "development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md", "DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md", "testing/SUPER_TESTERATOR_FIXES_COMPLETE.md", "atoms/commands/testing.md", "development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md", "operations/VERCEL_DEPLOYMENT_GUIDE.md", "testing/legacy/DASHBOARD_TEST_REPORT.md", "DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY.md", "SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL.md", "features/NAVIGATION_CLEANUP.md", "testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md", "operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md", "templates/DOCUMENT_TEMPLATE.md", "operations/VERCEL_DEPLOYMENT_SUMMARY.md", "development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md", "IMPLEMENTATION_COMPLETE_SUMMARY.md", "DOCUMENTATION_UPDATE_JUNE_2025.md"], "most_used_files": [["operations/README.md", 8], ["README.md", 7], ["project-management/03_TECH_SPECS.md", 7], ["user-guides/API.md", 6], ["project-management/02_ARCHITECTURE.md", 6], ["project-management/00_PROJECT_OVERVIEW.md", 5], ["user-guides/troubleshooting-guide.md", 5], ["DOCUMENTATION_INDEX.md", 5], ["development/README.md", 4], ["testing/README.md", 4]], "most_dependent_files": [["README.md", 43], ["DOCUMENTATION_MIGRATION_GUIDE.md", 19], ["development/README.md", 19], ["project-management/README.md", 17], ["testing/README.md", 15], ["project-management/ORGANIZATION_COMPLETE_SUMMARY.md", 6], ["user-guides/README.md", 6], ["DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md", 5], ["DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md", 5], ["operations/README.md", 5]], "circular_dependencies": [], "categories": {"unknown": 111, "atoms": 2, "workflows": 1}, "freshness": {"stale_files": []}}}