# Documentation Usage Report

Generated: 2025-06-15 22:17:51

## Summary

- **Total Files**: 114
- **Files with Dependencies**: 20
- **Total Dependencies**: 168
- **Orphaned Files**: 54

## Most Used Files

- **operations/README.md**: 8 references
- **README.md**: 7 references
- **project-management/03_TECH_SPECS.md**: 7 references
- **user-guides/API.md**: 6 references
- **project-management/02_ARCHITECTURE.md**: 6 references
- **project-management/00_PROJECT_OVERVIEW.md**: 5 references
- **user-guides/troubleshooting-guide.md**: 5 references
- **DOCUMENTATION_INDEX.md**: 5 references
- **development/README.md**: 4 references
- **testing/README.md**: 4 references

## Orphaned Files

- testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md
- testing/legacy/PROFILE_TESTING_CHECKLIST.md
- testing/reports/TESTING_INFRASTRUCTURE_FIXED.md
- resource-improvement-summary.md
- DOCUMENTATION_UPDATE_ASSESSMENT_TESTING.md
- PROJECT_NAVIGATION_SYSTEM.md
- development/PHASE1_IMPLEMENTATION_COMPLETE.md
- DOCUMENTATION_REORGANIZATION_SUMMARY.md
- testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md
- development/PHASE1_IMPLEMENTATION_PLAN.md
- DOCUMENTATION_ORGANIZATION_SYSTEM.md
- development/IMPLEMENTATION_COMPLETE.md
- features/MAJOR_DUPLICATION_CLEANUP.md
- testing/legacy/ASSESSMENT_TESTING_SUMMARY.md
- features/PROGRESS_ANALYTICS_CONSOLIDATION.md
- api/FREEDOM_FUND_API_VERIFICATION.md
- ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL.md
- development/PHASE1_SETUP_GUIDE.md
- development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md
- operations/COMPLETE_CLEANUP_GUIDE.md
- development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md
- DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md
- features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md
- testing/reports/FINAL_TEST_EXECUTION_REPORT.md
- development/DOCUMENTATION_UPDATE_SUMMARY.md
- development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md
- testing/legacy/ASSESSMENT_TESTING_PLAN.md
- DOCUMENTATION_MIGRATION_GUIDE.md
- UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE.md
- UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md
- operations/DEPLOYMENT_CHECKLIST.md
- testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md
- DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025.md
- STYLE_GUIDE.md
- project-management/ORGANIZATION_COMPLETE_SUMMARY.md
- testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md
- DOCUMENTATION_UPDATE_DATABASE_MIGRATION.md
- development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md
- DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025.md
- testing/SUPER_TESTERATOR_FIXES_COMPLETE.md
- atoms/commands/testing.md
- development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md
- operations/VERCEL_DEPLOYMENT_GUIDE.md
- testing/legacy/DASHBOARD_TEST_REPORT.md
- DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY.md
- SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL.md
- features/NAVIGATION_CLEANUP.md
- testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md
- operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md
- templates/DOCUMENT_TEMPLATE.md
- operations/VERCEL_DEPLOYMENT_SUMMARY.md
- development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md
- IMPLEMENTATION_COMPLETE_SUMMARY.md
- DOCUMENTATION_UPDATE_JUNE_2025.md

