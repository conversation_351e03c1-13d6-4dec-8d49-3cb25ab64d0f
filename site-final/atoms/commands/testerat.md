---
ai_context: Testerat AI testing tool commands for FAAFO Career Platform
built_at: '2025-06-15T22:35:47.614665'
category: atoms
dependencies: []
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: testing-team
source_file: atoms/commands/testerat.md
subcategory: commands
tags:
- testerat
- testing
- commands
- ai-testing
title: Testerat Commands
used_by: []
---

## Testerat Commands

### **Basic Testing**
```bash
# Test local development server
python3 testerat http://localhost:3000

# Test production site
python3 testerat https://your-app.com

# Demo mode (no URL required)
python3 testerat
```

### **Security Audit**
```bash
# Security-focused testing
python3 testerat https://your-app.com "security audit"

# FAAFO-specific security testing
python3 testerat http://localhost:3000 "FAAFO Security Audit"
```

### **Comprehensive Testing**
```bash
# Full FAAFO testing suite
python3 testerat http://localhost:3000 "FAAFO Career Platform Testing"

# Custom test description
python3 testerat <url> "Custom Test Description"
```

### **CI/CD Integration**
```bash
# Example CI/CD usage
python3 testerat $STAGING_URL "CI/CD Security Audit"

# Process JSON report for automated decisions
cat testerat_report.json | jq '.security_score'
```

### **Output Files**
- **HTML Report**: `testerat_report.html` (visual report)
- **JSON Report**: `testerat_report.json` (machine-readable)
- **Screenshots**: Saved in current directory

### **Documentation Access**
```bash
# Quick reference
docs/testing/core/TESTERAT_QUICK_REFERENCE.md

# Complete guide
docs/testing/core/TESTERAT_GUIDE.md
```
