# 🧭 Project Navigation System

## 🎯 Purpose

This document establishes a systematic approach for finding ANY file in the project quickly and reliably. It creates predictable patterns so anyone (developers, AI assistants, new team members) always knows where to look.

## 📍 Universal Navigation Principles

### **1. Start Points (Always Check These First)**
```
📁 Root Level Entry Points:
├── README.md                    # Project overview & quick navigation
├── DOCUMENTATION_INDEX.md       # Complete documentation map
└── package.json                 # Dependencies & scripts

📁 Application Entry Points:
├── faafo-career-platform/README.md     # App-specific setup
├── faafo-career-platform/package.json  # App dependencies
└── faafo-career-platform/src/app/      # Next.js routes
```

### **2. Category-Based Lookup System**
```
🎯 Looking for...                    👀 Check here first:
────────────────────────────────────────────────────────
📚 Documentation                    → docs/
🏗️  Source Code                     → faafo-career-platform/src/
🧪 Tests                           → faafo-career-platform/__tests__/
⚙️  Configuration                   → faafo-career-platform/ (root)
🗄️  Database                       → faafo-career-platform/prisma/
🎨 Static Assets                   → faafo-career-platform/public/
📧 Email Templates                 → faafo-career-platform/src/emails/
🔧 Scripts & Tools                 → scripts/
📦 Backups                         → backups/
```

### **3. Documentation Lookup Map**
```
🎯 Looking for...                    👀 Check here:
────────────────────────────────────────────────────────
📋 Project Requirements             → docs/project-management/
🏗️  Architecture & Design           → docs/project-management/
🔧 Implementation Details          → docs/development/
🧪 Testing Information             → docs/testing/
📖 User Documentation              → docs/user-guides/
⚙️  Deployment & Operations         → docs/operations/
🎯 Project Organization            → docs/ (root level)
```

## 🔍 File Finding Strategies

### **Strategy 1: Hierarchical Search**
1. **Start with entry points** (README files)
2. **Check category directories** based on file type
3. **Look in subdirectories** following naming patterns
4. **Use search tools** as backup

### **Strategy 2: Naming Pattern Recognition**
```
📝 File Type Patterns:
────────────────────────────────────────────────────────
*_GUIDE.md                      → docs/operations/ or docs/user-guides/
*_SUMMARY.md                    → docs/development/ or docs/testing/
*_REPORT.md                     → docs/testing/
*_PLAN.md                       → docs/development/ or docs/testing/
PHASE*_*.md                     → docs/development/
*_TEST*.md                      → docs/testing/
API*.md                         → docs/user-guides/
README.md                       → Any directory (navigation)
```

### **Strategy 3: Purpose-Based Lookup**
```
🎯 Purpose                          👀 Location Pattern:
────────────────────────────────────────────────────────
Setting up development             → faafo-career-platform/README.md
Understanding project goals        → docs/project-management/00_PROJECT_OVERVIEW.md
Learning the architecture          → docs/project-management/02_ARCHITECTURE.md
Finding API documentation          → docs/user-guides/API.md
Troubleshooting issues             → docs/user-guides/*troubleshooting*
Running tests                      → docs/testing/TESTING_GUIDE.md
Deploying the application          → docs/operations/deployment.md
Understanding file organization    → docs/PROJECT_STRUCTURE_GUIDE.md
```

## 🛠️ Navigation Tools & Commands

### **Quick Find Commands**
```bash
# Find any file by name
find . -name "*filename*" -type f | grep -v node_modules

# Find documentation by topic
find docs/ -name "*topic*" -type f

# Find all README files (navigation points)
find . -name "README.md" -type f | grep -v node_modules

# Find all configuration files
find . -name "*.config.*" -o -name "*.json" | grep -v node_modules

# Find test files
find . -name "*test*" -o -name "*spec*" | grep -v node_modules
```

### **Content Search Commands**
```bash
# Search for content across all documentation
grep -r "search term" docs/

# Search in source code
grep -r "search term" faafo-career-platform/src/

# Search in specific file types
find . -name "*.md" -exec grep -l "search term" {} \;
```

## 📚 Master File Index

### **Critical Navigation Files (Always Exist)**
```
📍 Universal Entry Points:
├── README.md                           # Project overview
├── DOCUMENTATION_INDEX.md              # Complete doc map
└── docs/README.md                      # Documentation hub

📍 Category Navigation:
├── docs/project-management/README.md   # Project docs
├── docs/development/README.md          # Dev docs
├── docs/testing/README.md              # Test docs
├── docs/user-guides/README.md          # User docs
└── docs/operations/README.md           # Ops docs

📍 Application Navigation:
├── faafo-career-platform/README.md     # App setup
└── faafo-career-platform/package.json  # Dependencies
```

### **Predictable File Locations**
```
📁 Always in these locations:
────────────────────────────────────────────────────────
Project requirements               → docs/project-management/01_REQUIREMENTS.md
System architecture               → docs/project-management/02_ARCHITECTURE.md
Technical specifications          → docs/project-management/03_TECH_SPECS.md
Main user guide                   → docs/user-guides/user-guide.md
API documentation                 → docs/user-guides/API.md
Testing guide                     → docs/testing/TESTING_GUIDE.md
Deployment guide                  → docs/operations/deployment.md
Database schema                   → faafo-career-platform/prisma/schema.prisma
Main application entry            → faafo-career-platform/src/app/page.tsx
```

## 🎯 Quick Reference Cheat Sheet

### **"I need to find..." → "Look here first:"**
```
🔍 Project overview                → README.md
🔍 All documentation              → DOCUMENTATION_INDEX.md
🔍 How to set up the app          → faafo-career-platform/README.md
🔍 System requirements            → docs/project-management/01_REQUIREMENTS.md
🔍 How the system works           → docs/project-management/02_ARCHITECTURE.md
🔍 API endpoints                  → docs/user-guides/API.md
🔍 How to run tests               → docs/testing/TESTING_GUIDE.md
🔍 How to deploy                  → docs/operations/deployment.md
🔍 Database structure             → faafo-career-platform/prisma/schema.prisma
🔍 React components               → faafo-career-platform/src/components/
🔍 API routes                     → faafo-career-platform/src/app/api/
🔍 Page components                → faafo-career-platform/src/app/
🔍 Utility functions              → faafo-career-platform/src/lib/
🔍 Test files                     → faafo-career-platform/__tests__/
🔍 Static assets                  → faafo-career-platform/public/
🔍 Email templates                → faafo-career-platform/src/emails/
🔍 Type definitions               → faafo-career-platform/src/types/
🔍 Configuration files            → faafo-career-platform/ (root)
🔍 Build scripts                  → scripts/
🔍 Project organization rules     → docs/PROJECT_STRUCTURE_GUIDE.md
🔍 Development conventions        → docs/PROJECT_CONVENTIONS.md
```

## 🤖 AI Assistant Instructions

When helping with this project, always:

1. **Start with entry points**: Check README.md and DOCUMENTATION_INDEX.md first
2. **Use category lookup**: Match the request to the appropriate docs/ category
3. **Follow naming patterns**: Use predictable file naming to locate files
4. **Check multiple locations**: Some topics might span multiple categories
5. **Validate with tools**: Use find commands to verify file locations
6. **Update navigation**: If files are moved, update relevant navigation documents

## 🔄 Maintenance

### **Monthly Tasks**
- [ ] Verify all navigation links work
- [ ] Update file location references
- [ ] Check for new files that need categorization
- [ ] Validate navigation patterns are being followed

### **When Adding New Files**
- [ ] Follow established location patterns
- [ ] Update relevant README files
- [ ] Add to DOCUMENTATION_INDEX.md if applicable
- [ ] Consider if new navigation patterns are needed

---

**Remember**: The goal is **predictability**. Anyone should be able to find any file by following these patterns, even without prior knowledge of the project structure.
