---
ai_context: URL validation and resource quality procedures for FAAFO Career Platform
built_at: '2025-06-15T22:42:55.197789'
category: atoms
dependencies: []
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: quality-team
source_file: atoms/procedures/url-validation.md
subcategory: procedures
tags:
- url
- validation
- resources
- quality-assurance
title: URL Validation Procedures
used_by: []
---

## URL Validation Procedures

### **Automated URL Validation**
```bash
# Run URL validation script
cd faafo-career-platform
npm run validate:urls

# Check specific resource URLs
npm run validate:urls -- --category="cybersecurity"

# Generate URL health report
npm run urls:health-report
```

### **Manual URL Verification**
```bash
# Test individual URLs
curl -I "https://example.com/resource"

# Batch test URLs from file
while read url; do
  curl -I "$url" | head -1
done < urls.txt
```

### **Database URL Updates**
```sql
-- Fix broken URLs (example)
UPDATE learning_resources 
SET url = 'https://new-working-url.com'
WHERE url = 'https://broken-url.com';

-- Bulk URL replacement
UPDATE learning_resources 
SET url = REPLACE(url, 'old-domain.com', 'new-domain.com')
WHERE url LIKE '%old-domain.com%';
```

### **Quality Standards**
- **Success Rate Target**: 99%+ working URLs
- **Response Time**: < 5 seconds for validation
- **Update Frequency**: Weekly automated checks
- **Manual Review**: Monthly for critical resources

### **Common Fixes**
- Replace `example.com` test URLs with real resources
- Update broken educational platform URLs
- Replace timeout-prone URLs with reliable alternatives
- Correct malformed URLs and redirects

### **Validation Results**
```bash
# Expected output format:
# ✅ 245/250 URLs working (98% success rate)
# ❌ 5 broken URLs found
# ⚠️  3 URLs with slow response times
```
