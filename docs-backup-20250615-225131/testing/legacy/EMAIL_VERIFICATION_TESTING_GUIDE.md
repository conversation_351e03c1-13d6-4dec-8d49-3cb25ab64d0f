# Email Verification System Testing Guide

## Overview

This guide provides comprehensive testing instructions for the email verification system implemented in the FAAFO Career Platform. The system includes user registration with email verification, secure token handling, and complete UI flows.

## 🧪 Test Structure

### 1. Unit Tests (Jest)
Located in `__tests__/` directory:

```
__tests__/
├── api/
│   ├── auth/
│   │   ├── verify-email.test.ts
│   │   └── resend-verification.test.ts
│   └── signup.test.ts
├── components/
│   ├── SignupForm.test.tsx
│   └── LoginForm.test.tsx
├── integration/
│   └── email-verification-flow.test.ts
└── lib/
    └── email-verification.test.ts
```

### 2. Manual Testing Scripts
- `scripts/manual-test-email-verification.ts` - API endpoint testing
- `scripts/test-email-verification.ts` - Comprehensive test runner

## 🚀 Running Tests

### Option 1: Jest Unit Tests (Recommended for CI/CD)

```bash
# Run all email verification tests
npm test -- __tests__/

# Run specific test suites
npm test -- __tests__/api/
npm test -- __tests__/components/
npm test -- __tests__/integration/

# Run with coverage
npm test -- --coverage
```

### Option 2: Manual API Testing

```bash
# Start the development server
npm run dev

# In another terminal, run manual tests
npx tsx scripts/manual-test-email-verification.ts
```

### Option 3: Comprehensive Test Suite

```bash
# Run the complete test suite with reporting
npx tsx scripts/test-email-verification.ts
```

## 📋 Test Coverage Areas

### 🔐 Security & Authentication
- [x] Token generation and validation
- [x] Token expiration handling (24-hour expiry)
- [x] Rate limiting (5-minute cooldown between emails)
- [x] Email/token mismatch protection
- [x] Secure cleanup of expired tokens
- [x] Prevention of unverified user login

### 📧 Email Integration
- [x] Verification email sending on signup
- [x] Email template rendering with proper links
- [x] Error handling for email service failures
- [x] Resend verification functionality
- [x] Proper email content and formatting

### 🎯 API Endpoints
- [x] `POST /api/signup` - User registration with verification
- [x] `POST /api/auth/verify-email` - Email verification
- [x] `GET /api/auth/verify-email` - Token validation
- [x] `POST /api/auth/resend-verification` - Resend verification email
- [x] `GET /api/auth/verification-status` - Check verification status

### 🎨 User Interface
- [x] SignupForm with verification flow
- [x] LoginForm with verification error handling
- [x] Email verification page with status indicators
- [x] Verification success/error states
- [x] Loading states and user feedback
- [x] Responsive design and dark theme support

### 🔄 Integration Flows
- [x] Complete signup → verification → login flow
- [x] Expired token handling and recovery
- [x] Already verified user scenarios
- [x] Rate limiting enforcement
- [x] Error recovery and user guidance

## 🧩 Test Scenarios

### 1. Happy Path Testing
```
1. User signs up with email/password
2. Verification email is sent
3. User clicks verification link
4. Email is verified successfully
5. User can now log in
```

### 2. Error Handling Testing
```
1. Invalid verification tokens
2. Expired verification tokens
3. Email service failures
4. Rate limiting scenarios
5. Already verified users
6. Non-existent users
```

### 3. Security Testing
```
1. Token tampering attempts
2. Email/token mismatch
3. Replay attack prevention
4. Rate limiting bypass attempts
5. Unverified user login prevention
```

## 🔧 Manual Testing Checklist

### Registration Flow
- [ ] User can register with valid email/password
- [ ] Verification email is sent immediately
- [ ] Registration success page shows verification instructions
- [ ] Resend verification button works
- [ ] "Register different email" option works

### Email Verification
- [ ] Verification link opens correct page
- [ ] Valid tokens verify successfully
- [ ] Invalid tokens show appropriate errors
- [ ] Expired tokens show resend option
- [ ] Already verified users see success message

### Login Protection
- [ ] Unverified users cannot log in
- [ ] Clear error message with resend option
- [ ] Verified users can log in normally
- [ ] Resend verification from login works

### UI/UX Testing
- [ ] All forms are responsive
- [ ] Dark theme works correctly
- [ ] Loading states are shown
- [ ] Error messages are clear
- [ ] Success feedback is provided

## 🐛 Common Issues & Debugging

### Email Not Sending
```bash
# Check email service configuration
echo $RESEND_API_KEY
echo $NEXTAUTH_URL

# Check email service logs
npm run dev
# Look for email sending errors in console
```

### Database Issues
```bash
# Reset database and run migrations
npx prisma db push
npx prisma generate

# Check verification tokens table
npx prisma studio
```

### Token Issues
```bash
# Check token generation
node -e "console.log(require('uuid').v4())"

# Check token expiry calculation
node -e "console.log(new Date(Date.now() + 24*60*60*1000))"
```

## 📊 Test Metrics

### Expected Test Results
- **Unit Tests**: 95%+ pass rate
- **Integration Tests**: 100% pass rate
- **Manual Tests**: All scenarios working
- **Code Coverage**: 80%+ for verification code

### Performance Benchmarks
- **Email Sending**: < 2 seconds
- **Token Verification**: < 500ms
- **Page Load**: < 1 second
- **API Response**: < 200ms

## 🚀 Deployment Testing

### Pre-deployment Checklist
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Email service configured
- [ ] Database migrations applied
- [ ] Environment variables set

### Production Testing
- [ ] Test with real email addresses
- [ ] Verify email delivery
- [ ] Test rate limiting
- [ ] Monitor error rates
- [ ] Check verification success rates

## 📝 Test Reporting

Tests generate detailed reports in:
- `EMAIL_VERIFICATION_TEST_REPORT.md` - Comprehensive test results
- Console output with pass/fail status
- Coverage reports in `coverage/` directory

## 🔄 Continuous Integration

Add to your CI/CD pipeline:

```yaml
# .github/workflows/test.yml
- name: Run Email Verification Tests
  run: |
    npm test -- __tests__/
    npx tsx scripts/test-email-verification.ts
```

## 📞 Support

If tests fail or you encounter issues:

1. Check the test output for specific error messages
2. Verify database connection and schema
3. Confirm email service configuration
4. Review environment variables
5. Check application logs for detailed errors

---

**Last Updated**: Generated with Email Verification System Implementation
**Test Coverage**: API Routes, UI Components, Integration Flows, Security Features
