# Documentation Style Guide
## FAAFO Career Platform Documentation Engineering

### 🎯 Purpose
This style guide ensures consistent, maintainable, and AI-optimized documentation across the FAAFO Career Platform project.

### 📐 Atomic Design Principles

#### **Atoms** (Single Source of Truth)
- **Location**: `docs/atoms/`
- **Purpose**: Reusable content blocks that exist once and are included elsewhere
- **Size**: 5-50 lines maximum
- **Examples**: Setup procedures, command snippets, configuration blocks

#### **Workflows** (Complete Processes)
- **Location**: `docs/workflows/`
- **Purpose**: Complete end-to-end processes composed from atoms
- **Size**: Context-complete but modular
- **Examples**: Testing workflows, deployment processes, development setup

#### **Reference** (Auto-generated)
- **Location**: `docs/reference/`
- **Purpose**: Generated documentation (API docs, schemas, etc.)
- **Maintenance**: Automated generation only

#### **Archives** (Legacy Content)
- **Location**: `docs/archives/`
- **Purpose**: Historical documentation for reference
- **Maintenance**: Read-only, no updates

### 🏗️ File Structure Standards

#### **Metadata Requirements**
Every documentation file MUST include frontmatter:

```yaml
---
title: "Clear, Descriptive Title"
category: "atoms|workflows|reference|archives"
subcategory: "setup|commands|concepts|procedures|testing|deployment"
tags: ["tag1", "tag2", "tag3"]
last_updated: "YYYY-MM-DD"
last_validated: "YYYY-MM-DD"
dependencies: ["atom1.md", "atom2.md"]
used_by: ["workflow1.md", "workflow2.md"]
maintainer: "team-name"
ai_context: "Brief description for AI understanding"
---
```

#### **Content Structure**
1. **Purpose Statement** (1-2 sentences)
2. **Prerequisites** (if any)
3. **Main Content** (structured with clear headings)
4. **Related Resources** (links to atoms/workflows)
5. **Validation Checklist** (how to verify success)

### 📝 Writing Standards

#### **Clarity & Conciseness**
- Use active voice
- Write for both humans and AI
- Include context in every atom
- Avoid assumptions about prior knowledge

#### **Consistency**
- Use consistent terminology (see glossary)
- Follow established patterns
- Maintain uniform formatting

#### **Semantic Structure**
- Use descriptive headings
- Include semantic tags
- Provide clear relationships between content

### 🔗 Linking & Inclusion Standards

#### **Transclusion Syntax**
```markdown
<!-- Include an atom -->
{% include "atoms/setup/environment.md" %}

<!-- Include with context -->
{% include "atoms/commands/testing.md" context="integration-testing" %}
```

#### **Cross-References**
```markdown
<!-- Link to related workflow -->
See also: [Complete Testing Workflow](../workflows/testing.md)

<!-- Link to specific atom -->
Prerequisites: [Environment Setup](../atoms/setup/environment.md)
```

### 🎨 Formatting Standards

#### **Code Blocks**
```bash
# Always include context comments
# This command starts the development server
npm run dev
```

#### **Callouts**
```markdown
> **⚠️ Warning**: Critical information that could cause issues
> **💡 Tip**: Helpful suggestions for better outcomes
> **📝 Note**: Additional context or clarification
```

#### **Lists**
- Use bullet points for unordered items
- Use numbers for sequential steps
- Include brief explanations for complex items

### 🔍 AI Optimization

#### **Semantic Context**
- Include purpose statements
- Provide clear categorization
- Use descriptive metadata

#### **Searchability**
- Include relevant keywords
- Use consistent terminology
- Provide multiple access paths

#### **Composability**
- Design atoms for reuse
- Create clear dependencies
- Enable flexible composition

### ✅ Quality Standards

#### **Validation Requirements**
- All links must resolve
- All includes must exist
- Metadata must be complete
- Content must be current (< 90 days)

#### **Review Process**
1. Automated validation (CI/CD)
2. Peer review for new content
3. Regular freshness audits
4. Usage pattern analysis

### 🚫 Anti-Patterns

#### **Avoid These Practices**
- Duplicating content across files
- Creating monolithic documentation
- Using unclear or inconsistent naming
- Omitting metadata or context
- Creating circular dependencies

#### **Legacy Patterns to Eliminate**
- Multiple "DOCUMENTATION_*" files
- Scattered setup instructions
- Redundant API documentation
- Unclear file organization

### 📊 Metrics & Monitoring

#### **Success Indicators**
- Reduced file count (target: 30-40 from 80+)
- Eliminated redundancy (single source of truth)
- Improved AI query resolution time
- Reduced maintenance overhead

#### **Quality Metrics**
- Metadata completeness (target: 100%)
- Link health (target: 100%)
- Content freshness (target: 90% < 90 days)
- Usage distribution (atoms reused 3+ times)

---

**Last Updated**: 2025-06-15
**Version**: 1.0
**Status**: Active
