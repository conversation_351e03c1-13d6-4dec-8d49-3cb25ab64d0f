#!/usr/bin/env python3
"""
Scattered Documentation Consolidation Script
Consolidates all scattered documentation into the single source of truth atomic system.
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json

class ScatteredDocsConsolidator:
    def __init__(self, root_docs="docs", scattered_docs="faafo-career-platform/docs"):
        self.root_docs = Path(root_docs)
        self.scattered_docs = Path(scattered_docs)
        self.consolidation_log = []
        
        # Ensure target directories exist
        (self.root_docs / "archives").mkdir(exist_ok=True)
        (self.root_docs / "reference").mkdir(exist_ok=True)
        
    def log_action(self, action, source, target="", details=""):
        """Log consolidation actions."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "source": str(source),
            "target": str(target),
            "details": details
        }
        self.consolidation_log.append(entry)
        print(f"📝 {action}: {source} → {target} {details}")
        
    def categorize_file(self, file_path):
        """Determine the best category for a scattered file."""
        filename = file_path.name.lower()
        
        # Security-related files
        if 'security' in filename or 'critical' in filename:
            return 'archives', 'security-related'
            
        # Testing-related files
        if 'testing' in filename or 'test' in filename or 'assessment' in filename:
            return 'archives', 'testing-related'
            
        # AI/Analytics-related files
        if 'ai-insights' in filename or 'analytics' in filename:
            return 'archives', 'ai-analytics-related'
            
        # Project structure files
        if 'project' in filename or 'structure' in filename:
            return 'archives', 'project-structure-related'
            
        # Mindset/resources files
        if 'mindset' in filename or 'resources' in filename:
            return 'archives', 'resources-related'
            
        # Default to archives
        return 'archives', 'general'
        
    def consolidate_file(self, source_file):
        """Consolidate a single scattered file."""
        if not source_file.exists():
            self.log_action("SKIP", source_file, "", "(file not found)")
            return False
            
        # Determine target category
        category, reason = self.categorize_file(source_file)
        
        # Create target filename with timestamp to avoid conflicts
        timestamp = datetime.now().strftime("%Y%m%d")
        target_name = f"{source_file.stem}_scattered_{timestamp}{source_file.suffix}"
        target_path = self.root_docs / category / target_name
        
        try:
            # Move file to target location
            shutil.move(str(source_file), str(target_path))
            self.log_action("CONSOLIDATED", source_file, target_path, f"({reason})")
            return True
        except Exception as e:
            self.log_action("ERROR", source_file, "", f"Failed to consolidate: {str(e)}")
            return False
            
    def remove_empty_directories(self, directory):
        """Remove empty directories after consolidation."""
        try:
            for item in directory.iterdir():
                if item.is_dir():
                    self.remove_empty_directories(item)
                    
            # Remove directory if empty
            if directory.exists() and not any(directory.iterdir()):
                directory.rmdir()
                self.log_action("REMOVED", directory, "", "(empty directory)")
        except Exception as e:
            self.log_action("ERROR", directory, "", f"Failed to remove: {str(e)}")
            
    def consolidate_all(self):
        """Consolidate all scattered documentation."""
        print("🔄 Starting scattered documentation consolidation...")
        print(f"📁 Source: {self.scattered_docs}")
        print(f"📁 Target: {self.root_docs}")
        
        if not self.scattered_docs.exists():
            print(f"⚠️  Source directory {self.scattered_docs} does not exist")
            return 0
            
        # Find all markdown files in scattered docs
        scattered_files = list(self.scattered_docs.rglob("*.md"))
        
        if not scattered_files:
            print(f"✅ No scattered markdown files found in {self.scattered_docs}")
            return 0
            
        print(f"📄 Found {len(scattered_files)} scattered files to consolidate")
        
        # Consolidate each file
        consolidated_count = 0
        for file_path in scattered_files:
            if self.consolidate_file(file_path):
                consolidated_count += 1
                
        # Find and consolidate any other relevant files (JSON, etc.)
        other_files = []
        for ext in ['.json', '.txt', '.yml', '.yaml']:
            other_files.extend(list(self.scattered_docs.rglob(f"*{ext}")))
            
        for file_path in other_files:
            if self.consolidate_file(file_path):
                consolidated_count += 1
                
        # Remove empty directories
        if self.scattered_docs.exists():
            self.remove_empty_directories(self.scattered_docs)
            
        # Create consolidation summary
        self.create_consolidation_summary(consolidated_count)
        
        # Report results
        self.report_results(consolidated_count)
        
        return consolidated_count
        
    def create_consolidation_summary(self, consolidated_count):
        """Create a summary of the consolidation."""
        summary_content = f"""---
title: "Scattered Documentation Consolidation Summary"
category: "reference"
tags: ["consolidation", "cleanup", "scattered-docs", "single-source-truth"]
last_updated: "{datetime.now().strftime('%Y-%m-%d')}"
generated_date: "{datetime.now().strftime('%Y-%m-%d')}"
generator: "consolidate-scattered-docs.py"
ai_context: "Summary of scattered documentation consolidation into atomic design system"
---

# Scattered Documentation Consolidation Summary

**Consolidation Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Consolidation Tool**: consolidate-scattered-docs.py
**Status**: Completed

## Problem Solved

### **FORBIDDEN Duplicate Structure Eliminated**
- **Source**: `faafo-career-platform/docs/` (scattered documentation)
- **Target**: `docs/` (single source of truth)
- **Result**: ✅ **Single documentation location achieved**

## Files Consolidated

### Consolidated Files
"""
        
        # Group files by category
        categories = {}
        for entry in self.consolidation_log:
            if entry["action"] == "CONSOLIDATED":
                category = Path(entry["target"]).parent.name
                if category not in categories:
                    categories[category] = []
                categories[category].append(entry)
                
        for category, entries in categories.items():
            summary_content += f"\n#### {category}/ ({len(entries)} files)\n"
            for entry in entries:
                source_name = Path(entry["source"]).name
                target_name = Path(entry["target"]).name
                summary_content += f"- `{source_name}` → `{category}/{target_name}` {entry['details']}\n"
                
        summary_content += f"""

**Total Files Consolidated**: {consolidated_count}

## Benefits Achieved

### Before Consolidation
- ❌ Documentation scattered across multiple locations
- ❌ Duplicate directory structures (`docs/` and `faafo-career-platform/docs/`)
- ❌ Violation of "Single Source of Truth" principle
- ❌ Confusion about where to find/place documentation

### After Consolidation
- ✅ Single documentation location: `docs/`
- ✅ All content follows atomic design principles
- ✅ Clear directory structure enforced
- ✅ No duplicate documentation locations
- ✅ Compliance with documentation standards

## Atomic Design Compliance

### Structure Enforced
```
docs/                           # SINGLE SOURCE OF TRUTH
├── atoms/                      # Reusable components
├── workflows/                  # Complete processes
├── reference/                  # Generated/reference content
├── archives/                   # Legacy and consolidated content
├── project-management/         # Legacy project docs
├── development/               # Legacy development docs
├── testing/                   # Legacy testing docs
├── user-guides/               # Legacy user docs
├── operations/                # Legacy operations docs
└── features/                  # Legacy feature docs
```

### Forbidden Locations Eliminated
- ❌ `faafo-career-platform/docs/` → ✅ Consolidated to `docs/archives/`
- ❌ Scattered documentation → ✅ Single source of truth
- ❌ Duplicate structures → ✅ Unified organization

## Quality Improvements

### Documentation Organization
- All scattered content safely archived with timestamps
- Clear categorization by content type
- Preservation of all information (nothing lost)
- Compliance with atomic design principles

### System Health
- Single source of truth established
- No duplicate documentation locations
- Clear navigation paths
- Automated governance compliance

## Next Steps

1. **Validate Consolidation**
   ```bash
   # Verify no scattered docs remain
   find . -name "docs" -type d
   
   # Validate atomic system
   python3 scripts/validate-metadata.py
   python3 scripts/validate-includes.py
   ```

2. **Update Team Processes**
   - Train team on single documentation location
   - Update contribution guidelines
   - Establish monitoring for scattered docs

3. **Continuous Monitoring**
   - Prevent future scattered documentation
   - Enforce single source of truth
   - Monitor for duplicate structures

---

**Consolidation Completed Successfully** ✅
**Single Source of Truth Achieved** 🎯
**Atomic Design Compliance Enforced** 💯
"""
        
        # Save consolidation summary
        summary_path = self.root_docs / "reference" / "scattered-docs-consolidation-summary.md"
        
        with open(summary_path, 'w') as f:
            f.write(summary_content)
            
        self.log_action("CREATED", summary_path, "", "Consolidation summary")
        
    def save_consolidation_log(self):
        """Save detailed consolidation log."""
        log_path = self.root_docs / "reference" / "scattered-docs-consolidation-log.json"
        
        with open(log_path, 'w') as f:
            json.dump(self.consolidation_log, f, indent=2)
            
        print(f"📊 Consolidation log saved to {log_path}")
        
    def report_results(self, consolidated_count):
        """Report consolidation results."""
        print(f"\n📊 Consolidation Results:")
        print(f"✅ Files consolidated: {consolidated_count}")
        print(f"📝 Log entries: {len(self.consolidation_log)}")
        print(f"📁 Target directory: {self.root_docs}")
        
        if consolidated_count > 0:
            print(f"\n🎯 Success Metrics:")
            print(f"✅ Eliminated duplicate documentation structure")
            print(f"✅ Achieved single source of truth")
            print(f"✅ All scattered content safely archived")
            print(f"✅ Atomic design compliance enforced")
        
        print(f"\n🔍 Verification Commands:")
        print(f"# Check for remaining scattered docs")
        print(f"find . -name 'docs' -type d")
        print(f"")
        print(f"# Validate atomic system")
        print(f"python3 scripts/validate-metadata.py")
        print(f"python3 scripts/validate-includes.py")
        
        return consolidated_count

def main():
    consolidator = ScatteredDocsConsolidator()
    consolidated_count = consolidator.consolidate_all()
    consolidator.save_consolidation_log()
    
    if consolidated_count > 0:
        print(f"\n✅ Consolidation completed successfully!")
        print(f"📦 {consolidated_count} scattered files consolidated")
        print(f"🎯 Single source of truth achieved")
    else:
        print(f"\n✅ No scattered documentation found - system already clean!")

if __name__ == "__main__":
    main()
