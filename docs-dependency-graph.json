{"dependencies": {"STYLE_GUIDE.md": ["atoms/setup/environment.md"], "README.md": ["PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/ASSESSMENT_SYSTEM.md", "project-management/GLOSSARY.md", "development/PHASE2_IMPLEMENTATION_SUMMARY.md", "development/PHASE3_IMPLEMENTATION_SUMMARY.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md", "development/NAVIGATION_ENHANCEMENT_REPORT.md", "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md", "development/ENHANCED_FEATURES_IMPLEMENTATION.md", "development/IMPLEMENTATION_SUMMARY.md", "development/AI_AGENT_TRANSITION_SUMMARY.md", "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "features/ENHANCED_ASSESSMENT_RESULTS.md", "features/ASSESSMENT_RESULTS_INTEGRATION.md", "user-guides/user-guide.md", "user-guides/API.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/03_TECH_SPECS.md", "project-management/02_ARCHITECTURE.md", "user-guides/API.md", "user-guides/troubleshooting-guide.md", "development/AI_AGENT_TRANSITION_SUMMARY.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "user-guides/API.md"], "DOCUMENTATION_INDEX.md": ["PROJECT_MAP.md", "README.md"], "development/README.md": ["development/PHASE2_IMPLEMENTATION_SUMMARY.md", "development/PHASE3_IMPLEMENTATION_SUMMARY.md", "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md", "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md", "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "development/NAVIGATION_ENHANCEMENT_REPORT.md", "development/ENHANCED_FEATURES_IMPLEMENTATION.md", "development/IMPLEMENTATION_SUMMARY.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/BUILD_SYSTEM_FIXES_JUNE_2025.md", "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "development/URL_VALIDATION_IMPLEMENTATION.md", "development/../project-management/02_ARCHITECTURE.md", "development/../project-management/03_TECH_SPECS.md", "development/CODE_QUALITY_FIXES_SUMMARY.md", "development/../README.md"], "project-management/README.md": ["project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/ASSESSMENT_SYSTEM.md", "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md", "project-management/GLOSSARY.md", "project-management/00_PROJECT_OVERVIEW.md", "project-management/01_REQUIREMENTS.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "project-management/04_UX_GUIDELINES.md", "project-management/05_DATA_POLICY.md", "project-management/../README.md"], "project-management/ORGANIZATION_COMPLETE_SUMMARY.md": ["PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "PROJECT_STRUCTURE_GUIDE.md", "PROJECT_CONVENTIONS.md", "project-management/../DOCUMENTATION_INDEX.md"], "features/README.md": ["features/ENHANCED_ASSESSMENT_RESULTS.md", "features/ASSESSMENT_RESULTS_INTEGRATION.md", "features/../README.md"], "workflows/documentation-migration.md": ["atoms/concepts/directory-structure.md", "atoms/concepts/naming-conventions.md", "atoms/setup/environment.md"], "workflows/testing.md": ["atoms/setup/environment.md", "workflows/deployment.md"], "workflows/deployment.md": ["atoms/procedures/deployment-checklist.md", "workflows/testing.md", "workflows/development-setup.md"], "workflows/development-setup.md": ["atoms/setup/environment.md", "atoms/setup/database-setup.md", "atoms/commands/development.md", "workflows/testing.md"], "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md": ["README.md", "project-management/README.md", "project-management/03_TECH_SPECS.md", "development/README.md", "user-guides/API.md", "testing/README.md", "operations/README.md", "operations/maintenance.md", "operations/database-backup.md", "user-guides/README.md", "user-guides/faq-troubleshooting.md", "project-management/00_PROJECT_OVERVIEW.md", "user-guides/API.md", "user-guides/user-guide.md", "operations/deployment.md", "user-guides/troubleshooting-guide.md", "project-management/02_ARCHITECTURE.md", "project-management/03_TECH_SPECS.md", "README.md"], "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md": ["development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md", "README.md", "development/README.md", "project-management/07_PROJECT_STATUS.md", "DOCUMENTATION_INDEX.md"], "archives/DOCUMENTATION_REORGANIZATION_SUMMARY_20250615.md": [], "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md": ["archives/../README.md", "testing/README.md", "PROJECT_MAP.md", "DOCUMENTATION_INDEX.md"], "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md": ["DOCUMENTATION_INDEX.md", "PROJECT_MAP.md", "DOCUMENTATION_INDEX.md"], "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md": ["README.md", "features/README.md", "development/README.md", "testing/README.md"], "operations/README.md": ["operations/deployment.md", "operations/database-backup.md", "operations/maintenance.md", "operations/../README.md"], "operations/database-backup.md": ["operations/README.md", "operations/../README.md"], "operations/deployment.md": ["operations/README.md", "operations/../README.md"], "operations/maintenance.md": ["operations/README.md", "operations/../README.md"], "user-guides/README.md": ["user-guides/user-guide.md", "user-guides/API.md", "user-guides/faq-troubleshooting.md", "user-guides/troubleshooting-guide.md", "user-guides/../README.md"], "testing/README.md": ["testing/core/TESTERAT_GUIDE.md", "testing/core/TESTERAT_QUICK_REFERENCE.md", "testing/core/TESTING_GUIDE.md", "testing/core/testing-strategy.md", "testing/core/06_TESTING_FRAMEWORK.md", "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md", "testing/reports/COMPREHENSIVE_TESTING_REPORT.md", "testing/reports/FINAL_TESTING_REPORT.md", "testing/reports/TEST_EXECUTION_SUMMARY.md", "testing/reports/IMPLEMENTATION_TEST_REPORT.md", "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md", "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md", "testing/api-testing/REAL_DATABASE_TESTING.md", "testing/../README.md"], "testing/core/TESTERAT_QUICK_REFERENCE.md": ["README.md"], "testing/api-testing/REAL_DATABASE_TESTING.md": [], "templates/DOCUMENT_TEMPLATE.md": []}, "usage": {"atoms/setup/environment.md": ["STYLE_GUIDE.md", "workflows/documentation-migration.md", "workflows/testing.md", "workflows/development-setup.md"], "PROJECT_STRUCTURE_GUIDE.md": ["README.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "PROJECT_CONVENTIONS.md": ["README.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md", "project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "project-management/00_PROJECT_OVERVIEW.md": ["README.md", "README.md", "project-management/README.md", "project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/01_REQUIREMENTS.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/02_ARCHITECTURE.md": ["README.md", "README.md", "project-management/README.md", "project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/03_TECH_SPECS.md": ["README.md", "README.md", "project-management/README.md", "project-management/README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/04_UX_GUIDELINES.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/05_DATA_POLICY.md": ["README.md", "project-management/README.md", "project-management/README.md"], "project-management/ASSESSMENT_SYSTEM.md": ["README.md", "project-management/README.md"], "project-management/GLOSSARY.md": ["README.md", "project-management/README.md"], "development/PHASE2_IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/PHASE3_IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/CODE_QUALITY_FIXES_SUMMARY.md": ["README.md", "development/README.md", "development/README.md"], "development/FORUM_IMPROVEMENTS_DOCUMENTATION.md": ["README.md", "development/README.md"], "development/NAVIGATION_ENHANCEMENT_REPORT.md": ["README.md", "development/README.md"], "development/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": ["README.md", "development/README.md"], "development/COMMUNITY_FORUM_AND_PROGRESS_IMPROVEMENTS.md": ["README.md", "development/README.md"], "development/ENHANCED_FEATURES_IMPLEMENTATION.md": ["README.md", "development/README.md"], "development/IMPLEMENTATION_SUMMARY.md": ["README.md", "development/README.md"], "development/AI_AGENT_TRANSITION_SUMMARY.md": ["README.md", "README.md"], "development/NEXTJS_DOWNGRADE_RESOLUTION_JUNE_2025.md": ["README.md", "development/README.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md"], "features/ENHANCED_ASSESSMENT_RESULTS.md": ["README.md", "features/README.md"], "features/ASSESSMENT_RESULTS_INTEGRATION.md": ["README.md", "features/README.md"], "user-guides/user-guide.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "user-guides/API.md": ["README.md", "README.md", "README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "user-guides/faq-troubleshooting.md": ["README.md", "README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "user-guides/troubleshooting-guide.md": ["README.md", "README.md", "README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "user-guides/README.md"], "operations/database-backup.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md"], "operations/deployment.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md"], "operations/maintenance.md": ["README.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/README.md"], "PROJECT_MAP.md": ["DOCUMENTATION_INDEX.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md"], "README.md": ["DOCUMENTATION_INDEX.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md", "testing/core/TESTERAT_QUICK_REFERENCE.md"], "development/BUILD_SYSTEM_FIXES_JUNE_2025.md": ["development/README.md"], "development/URL_VALIDATION_IMPLEMENTATION.md": ["development/README.md"], "development/../project-management/02_ARCHITECTURE.md": ["development/README.md"], "development/../project-management/03_TECH_SPECS.md": ["development/README.md"], "development/../README.md": ["development/README.md"], "project-management/ASSESSMENT_IMPROVEMENTS_SUMMARY.md": ["project-management/README.md"], "project-management/../README.md": ["project-management/README.md"], "project-management/../DOCUMENTATION_INDEX.md": ["project-management/ORGANIZATION_COMPLETE_SUMMARY.md"], "features/../README.md": ["features/README.md"], "atoms/concepts/directory-structure.md": ["workflows/documentation-migration.md"], "atoms/concepts/naming-conventions.md": ["workflows/documentation-migration.md"], "workflows/deployment.md": ["workflows/testing.md"], "atoms/procedures/deployment-checklist.md": ["workflows/deployment.md"], "workflows/testing.md": ["workflows/deployment.md", "workflows/development-setup.md"], "workflows/development-setup.md": ["workflows/deployment.md"], "atoms/setup/database-setup.md": ["workflows/development-setup.md"], "atoms/commands/development.md": ["workflows/development-setup.md"], "project-management/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "development/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md"], "testing/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md"], "operations/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md"], "user-guides/README.md": ["archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md"], "project-management/07_PROJECT_STATUS.md": ["archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md"], "DOCUMENTATION_INDEX.md": ["archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md", "archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md", "archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md"], "archives/../README.md": ["archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md"], "features/README.md": ["archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md"], "operations/../README.md": ["operations/README.md", "operations/database-backup.md", "operations/deployment.md", "operations/maintenance.md"], "user-guides/../README.md": ["user-guides/README.md"], "testing/core/TESTERAT_GUIDE.md": ["testing/README.md"], "testing/core/TESTERAT_QUICK_REFERENCE.md": ["testing/README.md"], "testing/core/TESTING_GUIDE.md": ["testing/README.md"], "testing/core/testing-strategy.md": ["testing/README.md"], "testing/core/06_TESTING_FRAMEWORK.md": ["testing/README.md"], "testing/reports/COMPREHENSIVE_TEST_EXECUTION_REPORT_JUNE_2025.md": ["testing/README.md"], "testing/reports/COMPREHENSIVE_TESTING_REPORT.md": ["testing/README.md"], "testing/reports/FINAL_TESTING_REPORT.md": ["testing/README.md"], "testing/reports/TEST_EXECUTION_SUMMARY.md": ["testing/README.md"], "testing/reports/IMPLEMENTATION_TEST_REPORT.md": ["testing/README.md"], "testing/api-testing/ASSESSMENT_API_TESTING_COMPLETE.md": ["testing/README.md"], "testing/api-testing/FREEDOM_FUND_API_VERIFICATION.md": ["testing/README.md"], "testing/api-testing/REAL_DATABASE_TESTING.md": ["testing/README.md"], "testing/../README.md": ["testing/README.md"]}, "generated_at": "/Users/<USER>/faafo/faafo", "stats": {"total_files": 26, "files_with_dependencies": 23, "most_used_files": [["project-management/03_TECH_SPECS.md", 6], ["user-guides/API.md", 6], ["README.md", 6], ["project-management/00_PROJECT_OVERVIEW.md", 5], ["project-management/02_ARCHITECTURE.md", 5], ["user-guides/troubleshooting-guide.md", 5], ["atoms/setup/environment.md", 4], ["user-guides/faq-troubleshooting.md", 4], ["operations/README.md", 4], ["DOCUMENTATION_INDEX.md", 4]]}}