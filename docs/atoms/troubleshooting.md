---
title: "Troubleshooting - Core Atom"
type: "atom"
purpose: "Complete troubleshooting guide for common issues"
includes: ['Login Issues', 'Assessment Problems', 'API Errors', 'Performance']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# Troubleshooting - Core Atom

## Purpose
Comprehensive troubleshooting guide for FAAFO Career Platform covering common user and technical issues

## Quick Reference

### Key Components
- **Authentication Issues** - Login, account lockout, password reset
- **Assessment Problems** - Progress saving, submission, relevance
- **API Errors** - Freedom Fund, career paths, forum functionality
- **Performance Issues** - Loading speed, browser compatibility

## Complete Guide

### Prerequisites
- Basic understanding of web browsers and internet connectivity
- Access to FAAFO Career Platform account
- Knowledge of supported browsers and devices

### Authentication Issues

#### Login Problems
**Symptoms:** Login form shows error, can't access dashboard

**Solutions:**
1. **Verify Credentials**
   - Check email address for typos
   - Ensure password is correct (check caps lock)
   - Try typing password in text editor first

2. **Account Lockout (5 failed attempts)**
   - Wait 15 minutes for automatic unlock
   - Clear browser cache and cookies
   - Try incognito/private window

3. **Password Reset**
   - Click "Forgot Password?" on login page
   - Check email including spam folder
   - Follow reset link within 1 hour

#### Password Reset Email Issues
**Solutions:**
1. Check spam/junk folder thoroughly
2. Verify email address is correct
3. Add <EMAIL> to contacts
4. Try different email address if available
5. Contact support for manual assistance

### Assessment System Issues

#### Progress Not Saving
**Symptoms:** Losing progress when navigating away

**Solutions:**
1. **Internet Connection**
   - Ensure stable connection
   - Refresh page and retry
   - Test other websites

2. **Browser Issues**
   - Clear cache and cookies
   - Disable extensions temporarily
   - Try different browser

3. **Manual Saving**
   - Click "Save Progress" regularly
   - Don't rely only on auto-save
   - Complete in one session if possible

#### Submission Problems
**Solutions:**
1. Ensure all required questions answered
2. Check for error messages on specific questions
3. Try different browser
4. Clear cache and retry
5. Contact support with assessment ID

#### Irrelevant Suggestions
**Solutions:**
1. Review answers for accuracy
2. Consider retaking with thoughtful responses
3. Explore career paths manually
4. Remember suggestions are starting points

### Freedom Fund Calculator Issues

#### Incorrect Calculations
**Solutions:**
1. **Verify Inputs**
   - Check monthly (not yearly) amounts
   - Include only essential expenses
   - Ensure realistic estimates

2. **Calculation Errors**
   - Refresh page and re-enter data
   - Try different browser
   - Manual verify: Monthly × Months = Target

#### Progress Not Saving
**Solutions:**
1. Ensure logged in
2. Check internet connection
3. Try smaller amounts first
4. Clear browser cache

### API and Technical Issues

#### Freedom Fund API (401 Unauthorized)
**Expected Behavior:** API returns 401 for unauthenticated requests
**Solutions:**
1. Verify user is logged in
2. Check session validity
3. Refresh authentication token
4. Clear browser data and re-login

#### Career Paths Not Loading
**Solutions:**
1. Refresh the page
2. Check internet connection
3. Try different browser
4. Clear browser cache
5. Contact support if persistent

#### Forum Functionality
**Can't Create Posts:**
1. Verify logged in status
2. Check title and content filled
3. Ensure minimum length requirements
4. Try different browser
5. Check for error messages

**Posts Not Appearing:**
1. Refresh forum page
2. Check submission confirmation
3. Wait a few moments for processing
4. Contact support if >5 minutes

### Performance Issues

#### Slow Loading
**Solutions:**
1. **Internet Connection**
   - Test other websites
   - Try mobile data if on WiFi
   - Contact ISP for widespread issues

2. **Browser Optimization**
   - Close unnecessary tabs
   - Clear browser cache
   - Disable unnecessary extensions
   - Try incognito mode

3. **Device Optimization**
   - Restart browser
   - Restart device
   - Free up storage space

#### Features Not Working
**Solutions:**
1. **JavaScript Issues**
   - Ensure JavaScript enabled
   - Disable ad blockers temporarily
   - Try different browser

2. **Cache Problems**
   - Hard refresh (Ctrl+F5 or Cmd+Shift+R)
   - Clear browser cache and cookies
   - Try incognito/private mode

### Browser Compatibility

#### Supported Browsers
- **Chrome:** Version 90+
- **Firefox:** Version 88+
- **Safari:** Version 14+
- **Edge:** Version 90+

#### Mobile Compatibility
- **iOS:** Safari 14+ or Chrome 90+
- **Android:** Chrome 90+ or Firefox 88+
- **Minimum screen width:** 320px

#### Unsupported
- Internet Explorer (any version)
- Chrome versions below 90
- Very old mobile browsers

### Getting Help

#### Contact Support
1. Use Contact form with detailed description
2. Include browser type and version
3. Describe exact steps leading to problem
4. Include any error messages
5. For urgent issues: <EMAIL> with "URGENT" in subject

#### Community Forum
1. Search for similar issues first
2. Post question with details
3. Other users may have solutions
4. Be specific about error symptoms

### Prevention Best Practices

#### Regular Maintenance
1. Clear browser cache weekly
2. Keep browser updated
3. Maintain stable internet connection
4. Keep device storage >10% free

#### Account Security
1. Use strong, unique passwords
2. Don't share login credentials
3. Log out from shared devices
4. Report suspicious activity immediately

#### Optimal Usage
1. Save progress frequently
2. Use supported browsers
3. Disable unnecessary extensions
4. Complete assessments in one session when possible

### Common Error Codes

#### HTTP Status Codes
- **401 Unauthorized** - Login required or session expired
- **403 Forbidden** - Insufficient permissions
- **404 Not Found** - Resource doesn't exist
- **429 Too Many Requests** - Rate limit exceeded
- **500 Internal Server Error** - Server-side issue

#### Application Errors
- **Assessment validation failed** - Check required fields
- **Freedom Fund calculation error** - Verify input values
- **Session expired** - Re-login required
- **Network timeout** - Check internet connection

### Related Atoms
- **[Environment](./environment.md)** - Setup and configuration
- **[API](./api.md)** - API-specific troubleshooting
- **[Testing](./testing.md)** - Testing and validation

---
*This is a core atom - the single source of truth for troubleshooting information.*
