---
title: "User Guide - Core Atom"
type: "atom"
purpose: "Complete user guide for FAAFO Career Platform"
includes: ['Registration', 'Assessment', 'Career Paths', 'Freedom Fund']
last_updated: "2025-06-15"
maintainer: "product-team"
---

# User Guide - Core Atom

## Purpose
Complete user guide for FAAFO Career Platform covering all features and functionality

## Quick Reference

### Key Features
- **Career Assessment** - 6-step comprehensive evaluation
- **Career Path Exploration** - Personalized recommendations
- **Freedom Fund Calculator** - Financial planning tool
- **Community Forum** - Peer support and discussions

## Complete Guide

### Prerequisites
- Modern web browser (Chrome 90+, Firefox 88+, Safari 14+)
- Email address for account creation
- Basic understanding of career transition goals

### Getting Started

#### Account Registration
1. **Visit Platform**
   - Go to https://faafo-career-platform.vercel.app
   - Click "Sign Up" button

2. **Create Account**
   - Enter email address
   - Create strong password (12+ characters)
   - Verify email address from confirmation email

3. **Initial Setup**
   - Complete basic profile information
   - Set privacy preferences
   - Review platform features overview

#### First Login
1. **Sign In**
   - Use email and password
   - Check "Remember me" for convenience
   - Access main dashboard

2. **Dashboard Overview**
   - Assessment progress indicator
   - Quick access to key features
   - Recent activity summary
   - Recommended next steps

### Career Assessment System

#### Starting Your Assessment
1. **Access Assessment**
   - Click "Start Assessment" from dashboard
   - Review assessment overview and time estimate
   - Begin 6-step evaluation process

2. **Assessment Steps**
   - **Step 1: Current Situation** - Job satisfaction and challenges
   - **Step 2: Desired Outcomes** - Goals and aspirations
   - **Step 3: Skills Evaluation** - Current abilities and strengths
   - **Step 4: Values Clarification** - Core values and priorities
   - **Step 5: Readiness Assessment** - Transition preparedness
   - **Step 6: Personal Insights** - Open-ended reflections

#### Completing Assessment Questions
1. **Question Types**
   - Multiple choice (single selection)
   - Multiple choice (multiple selections)
   - Scale ratings (1-5)
   - Open-ended text responses

2. **Best Practices**
   - Answer honestly and thoughtfully
   - Take time to reflect on each question
   - Use "Save Progress" regularly
   - Complete in one session if possible

3. **Progress Saving**
   - Auto-save every 30 seconds
   - Manual save with "Save Progress" button
   - Resume from any step if interrupted

#### Understanding Your Results
1. **Assessment Scores**
   - **Overall Readiness Score** (0-100)
   - **Financial Readiness** - Emergency fund and planning
   - **Risk Tolerance** - Comfort with uncertainty
   - **Skills Confidence** - Ability to transition
   - **Support Level** - Available resources and network

2. **Personalized Recommendations**
   - Career path suggestions based on responses
   - Obstacle identification and solutions
   - Timeline recommendations
   - Next steps prioritization

3. **Results Actions**
   - Download results summary
   - Bookmark recommended career paths
   - Share insights with mentors/coaches
   - Retake assessment to track progress

### Career Path Exploration

#### Browsing Career Paths
1. **Access Career Paths**
   - Navigate to "Career Paths" section
   - View all available paths
   - Filter by industry, skills, or interests

2. **Path Information**
   - **Overview** - General description and requirements
   - **Pros & Cons** - Benefits and challenges
   - **Action Steps** - Specific steps to get started
   - **Skills Required** - Necessary abilities and knowledge
   - **Timeline** - Typical transition duration

#### Using Recommendations
1. **Assessment-Based Suggestions**
   - View paths recommended from assessment
   - Understand why each path was suggested
   - Compare multiple recommended options

2. **Path Comparison**
   - Side-by-side comparison tool
   - Pros/cons analysis
   - Skills gap identification
   - Timeline comparison

#### Bookmarking and Tracking
1. **Bookmark Paths**
   - Save interesting career paths
   - Add personal notes and thoughts
   - Track exploration progress

2. **Action Planning**
   - Create custom action plans
   - Set milestones and deadlines
   - Track completion progress
   - Update plans as needed

### Freedom Fund Calculator

#### Setting Up Your Freedom Fund
1. **Access Calculator**
   - Navigate to "Freedom Fund" section
   - Review calculator overview
   - Start financial planning process

2. **Input Your Information**
   - **Monthly Essential Expenses** - Housing, food, utilities, insurance
   - **Coverage Period** - Choose 3, 6, 9, or 12 months
   - **Current Savings** - Amount already saved
   - **Target Date** - When you want to reach your goal

3. **Understanding Calculations**
   - **Target Amount** = Monthly Expenses × Coverage Months
   - **Inflation Adjustment** - 3% annual rate applied
   - **Progress Percentage** - Current savings / Target amount
   - **Monthly Savings Needed** - To reach goal by target date

#### Tracking Progress
1. **Regular Updates**
   - Update current savings amount monthly
   - Adjust expenses as they change
   - Modify target date if needed

2. **Progress Visualization**
   - Progress bar showing completion percentage
   - Amount remaining to reach goal
   - Timeline to goal achievement
   - Savings rate recommendations

3. **Goal Achievement**
   - Celebration when goal reached
   - Guidance on maintaining fund
   - Next steps for career transition

### Community Forum

#### Participating in Discussions
1. **Browse Forum**
   - View recent posts and discussions
   - Browse by category (introductions, advice, success stories)
   - Search for specific topics

2. **Creating Posts**
   - Click "New Post" button
   - Choose appropriate category
   - Write clear, descriptive title
   - Provide detailed content
   - Add relevant tags

3. **Engaging with Others**
   - Reply to posts with helpful insights
   - Like posts that resonate with you
   - Follow interesting discussions
   - Build connections with community members

#### Forum Guidelines
1. **Community Standards**
   - Be respectful and supportive
   - Share experiences and insights
   - Avoid promotional content
   - Respect privacy and confidentiality

2. **Getting Help**
   - Ask specific questions
   - Provide context for your situation
   - Be open to different perspectives
   - Thank community members for help

### Profile Management

#### Updating Your Profile
1. **Personal Information**
   - Name and contact details
   - Professional background
   - Career interests and goals
   - Privacy settings

2. **Assessment History**
   - View past assessment results
   - Compare scores over time
   - Track progress and changes
   - Download historical data

3. **Activity Tracking**
   - Forum participation history
   - Bookmarked career paths
   - Freedom Fund progress
   - Platform usage statistics

### Tips for Success

#### Maximizing Platform Value
1. **Regular Engagement**
   - Complete assessment thoroughly
   - Explore multiple career paths
   - Participate in community discussions
   - Update Freedom Fund progress regularly

2. **Goal Setting**
   - Set specific, measurable goals
   - Create realistic timelines
   - Track progress consistently
   - Celebrate milestones achieved

3. **Community Participation**
   - Share your experiences
   - Learn from others' journeys
   - Offer support and encouragement
   - Build meaningful connections

### Getting Support

#### Platform Support
- **Help Documentation** - Comprehensive guides and FAQs
- **Contact Form** - Direct support for technical issues
- **Community Forum** - Peer support and advice
- **Email Support** - <EMAIL> for urgent issues

#### Technical Issues
- See [Troubleshooting Guide](./troubleshooting.md) for common problems
- Check browser compatibility requirements
- Clear cache and cookies if experiencing issues
- Contact support with detailed error descriptions

### Related Atoms
- **[Troubleshooting](./troubleshooting.md)** - Technical issue resolution
- **[API](./api.md)** - Technical integration details
- **[Project Requirements](./project-requirements.md)** - Platform goals and features

---
*This is a core atom - the single source of truth for user guidance.*
