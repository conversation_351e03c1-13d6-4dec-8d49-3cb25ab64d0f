---
title: "Environment - Core Atom"
type: "atom"
purpose: "Complete environment setup (dev, test, prod)"
includes: ['Node.js', 'Database', 'Environment variables', 'Dependencies']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# Environment - Core Atom

## Purpose
Complete environment setup for FAAFO Career Platform (development, testing, production)

## Quick Reference

### Key Components
- **Node.js 18+** - JavaScript runtime
- **PostgreSQL/SQLite** - Database with Prisma ORM
- **Environment Variables** - Configuration and secrets
- **Dependencies** - npm packages and tools

## Complete Guide

### Prerequisites
- Node.js 18+ installed
- Git for version control
- Code editor (VS Code recommended)

### Core Setup

#### Node.js & Dependencies
```bash
# Clone repository
git clone https://github.com/dm601990/faafo.git
cd faafo/faafo-career-platform

# Install dependencies
npm install

# Verify installation
node --version  # Should be 18+
npm --version
```

#### Database Configuration
```bash
# Development (SQLite)
cp .env.example .env.local

# Production (PostgreSQL)
DATABASE_URL="postgresql://user:password@host:port/database"

# Run migrations
npx prisma migrate dev
npx prisma generate

# Seed database (optional)
npm run prisma:seed
```

#### Environment Variables
**Required Variables:**
```bash
# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Database
DATABASE_URL="file:./dev.db"  # SQLite for dev
# DATABASE_URL="postgresql://..." # PostgreSQL for prod

# Email (Optional)
RESEND_API_KEY="re_..."
EMAIL_FROM="<EMAIL>"

# Monitoring (Production)
SENTRY_DSN="https://..."
```

**Configuration Validation:**
```typescript
// Required environment variables are validated at startup
const required = [
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];
```

#### Development Tools
```bash
# Start development server
npm run dev

# Database management
npx prisma studio  # Database browser

# Code quality
npm run lint      # ESLint
npm run type-check # TypeScript
```

### Environment-Specific Configuration

#### Development
- **Database**: SQLite (`file:./dev.db`)
- **URL**: `http://localhost:3000`
- **Hot reload**: Enabled
- **Debug mode**: Enabled

#### Testing
- **Database**: In-memory SQLite
- **Mocks**: Comprehensive mocking system
- **Coverage**: Jest with coverage reports

#### Production
- **Database**: PostgreSQL (Vercel/Neon)
- **URL**: Production domain
- **Monitoring**: Sentry error tracking
- **Performance**: Optimized builds

### Best Practices

#### Security
1. **Never commit secrets** - Use `.env.local` for local secrets
2. **Validate environment** - Check required variables at startup
3. **Use strong secrets** - Generate secure NEXTAUTH_SECRET
4. **Database security** - Use connection pooling and timeouts

#### Performance
1. **Connection limits** - Configure database connection limits
2. **Caching** - Enable Redis for production
3. **Build optimization** - Use production builds
4. **Monitoring** - Set up error tracking and metrics

### Common Issues

#### Database Connection
- **Issue**: `PrismaClientInitializationError`
- **Solution**: Check DATABASE_URL format and database accessibility
- **Debug**: `npx prisma db pull` to test connection

#### Environment Variables
- **Issue**: Variables not loading
- **Solution**: Ensure `.env.local` exists and has correct format
- **Debug**: Check `process.env` in API routes

#### Port Conflicts
- **Issue**: Port 3000 already in use
- **Solution**: `npm run dev -- -p 3001` or kill existing process
- **Debug**: `lsof -i :3000` to find process using port

### Related Atoms
- **[Testing](./testing.md)** - Test environment setup
- **[Deployment](./deployment.md)** - Production environment
- **[API](./api.md)** - API configuration

---
*This is a core atom - the single source of truth for environment setup.*
