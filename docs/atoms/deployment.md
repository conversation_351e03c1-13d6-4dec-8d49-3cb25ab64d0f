---
title: "Deployment - Core Atom"
type: "atom"
purpose: "Complete deployment process (staging, production)"
includes: ['Build', 'Deploy', 'Verify', 'Rollback']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# Deployment - Core Atom

## Purpose
Complete deployment process for FAAFO Career Platform covering build, deploy, verify, and rollback

## Quick Reference

### Key Components
- **Vercel Platform** - Primary deployment target with automatic deployments
- **Build Process** - Next.js production builds with optimization
- **Environment Management** - Staging and production configurations
- **Database Migrations** - Prisma schema deployments

## Complete Guide

### Prerequisites
- Environment setup completed (see [Environment](./environment.md))
- Vercel account and project configured
- Database access (PostgreSQL for production)
- Understanding of CI/CD principles

### Deployment Architecture

#### Platform Overview
```bash
# Deployment Targets
- Production: https://faafo-career-platform.vercel.app
- Staging: https://faafo-career-platform-staging.vercel.app
- Preview: Automatic for pull requests

# Infrastructure
- Vercel Edge Functions
- PostgreSQL Database (Neon/Vercel)
- CDN for static assets
- Automatic SSL certificates
```

#### Build Configuration
```javascript
// next.config.js
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client']
  },
  images: {
    domains: ['images.unsplash.com', 'avatars.githubusercontent.com']
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  }
};
```

### Build Process

#### Local Build
```bash
# Development build
npm run dev

# Production build
npm run build
npm run start

# Build analysis
npm run build:analyze

# Type checking
npm run type-check
```

#### Production Build Steps
```bash
1. Install dependencies (npm ci)
2. Run type checking (tsc --noEmit)
3. Run linting (eslint)
4. Run tests (npm test)
5. Build application (next build)
6. Generate Prisma client
7. Run database migrations (if needed)
8. Deploy to Vercel
```

### Environment Configuration

#### Production Environment Variables
```bash
# Required for Production
DATABASE_URL="postgresql://user:pass@host:port/db"
NEXTAUTH_SECRET="production-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# Optional Services
RESEND_API_KEY="re_..."
EMAIL_FROM="<EMAIL>"
SENTRY_DSN="https://..."

# Database Configuration
DB_CONNECTION_TIMEOUT="10000"
DB_QUERY_TIMEOUT="5000"
DB_MAX_CONNECTIONS="10"
```

#### Staging Environment
```bash
# Staging-specific variables
DATABASE_URL="postgresql://staging-db-url"
NEXTAUTH_URL="https://staging-domain.com"
NODE_ENV="production"
VERCEL_ENV="preview"
```

### Database Deployment

#### Migration Strategy
```bash
# Development migrations
npx prisma migrate dev --name feature_name

# Production migrations
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset

# Generate client after schema changes
npx prisma generate
```

#### Database Deployment Checklist
```bash
1. ✅ Backup production database
2. ✅ Test migrations on staging
3. ✅ Verify schema changes
4. ✅ Run migration in production
5. ✅ Verify application functionality
6. ✅ Monitor for errors
```

### Deployment Process

#### Automatic Deployment (Recommended)
```bash
# Git-based deployment
git push origin main  # Deploys to production
git push origin develop  # Deploys to staging

# Pull request deployment
# Creates automatic preview deployment
# Runs all checks and tests
# Provides preview URL for testing
```

#### Manual Deployment
```bash
# Using Vercel CLI
npm install -g vercel
vercel login
vercel --prod  # Deploy to production
vercel  # Deploy to preview
```

#### Deployment Configuration
```json
// vercel.json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm ci",
  "framework": "nextjs",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret"
  }
}
```

### Verification Process

#### Post-Deployment Checks
```bash
# Health check endpoints
curl https://your-domain.com/api/health

# Database connectivity
curl https://your-domain.com/api/health/database

# Authentication system
curl https://your-domain.com/api/auth/session

# Core functionality
curl https://your-domain.com/api/career-paths
```

#### Automated Testing
```bash
# Run production tests
npm run test:production

# End-to-end testing
python3 testerat https://your-domain.com "production validation"

# Performance testing
npm run test:performance

# Security testing
npm run test:security
```

#### Monitoring Setup
```typescript
// Sentry Configuration
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.VERCEL_ENV || "development",
  tracesSampleRate: 1.0,
});
```

### Rollback Strategy

#### Immediate Rollback
```bash
# Vercel rollback to previous deployment
vercel rollback [deployment-url]

# Git-based rollback
git revert HEAD
git push origin main
```

#### Database Rollback
```bash
# Restore from backup (if schema changes)
pg_restore --clean --no-acl --no-owner -h host -U user -d database backup.sql

# Revert migrations (if possible)
npx prisma migrate reset
npx prisma migrate deploy
```

#### Emergency Procedures
```bash
1. 🚨 Identify issue severity
2. 📞 Notify team immediately
3. 🔄 Execute rollback procedure
4. 🔍 Investigate root cause
5. 📝 Document incident
6. 🛠️ Implement fix
7. ✅ Re-deploy with fix
```

### Best Practices

#### Pre-Deployment
1. **Test thoroughly** - Run full test suite
2. **Review changes** - Code review process
3. **Check dependencies** - Verify package updates
4. **Backup database** - Always backup before migrations
5. **Staging validation** - Test on staging first

#### During Deployment
1. **Monitor metrics** - Watch error rates and performance
2. **Gradual rollout** - Use feature flags when possible
3. **Communication** - Keep team informed
4. **Documentation** - Record deployment details

#### Post-Deployment
1. **Verify functionality** - Test critical user flows
2. **Monitor errors** - Check Sentry for new issues
3. **Performance check** - Verify response times
4. **User feedback** - Monitor for user reports

### Common Issues

#### Build Failures
- **Issue**: TypeScript compilation errors
- **Solution**: Fix type errors and run `npm run type-check`
- **Debug**: Check build logs for specific error details

#### Database Connection
- **Issue**: Database connection failures in production
- **Solution**: Verify DATABASE_URL and connection limits
- **Debug**: Check database logs and connection pooling

#### Environment Variables
- **Issue**: Missing or incorrect environment variables
- **Solution**: Verify all required variables are set in Vercel
- **Debug**: Check Vercel dashboard environment settings

#### Performance Issues
- **Issue**: Slow response times after deployment
- **Solution**: Check database queries and optimize if needed
- **Debug**: Use Vercel Analytics and database monitoring

### Deployment Checklist

#### Pre-Deployment
- [ ] All tests passing
- [ ] Code reviewed and approved
- [ ] Environment variables configured
- [ ] Database backup completed
- [ ] Staging deployment tested

#### Deployment
- [ ] Build completed successfully
- [ ] Database migrations applied
- [ ] Environment variables verified
- [ ] SSL certificates valid

#### Post-Deployment
- [ ] Health checks passing
- [ ] Core functionality verified
- [ ] Performance metrics normal
- [ ] Error monitoring active
- [ ] Team notified of deployment

### Related Atoms
- **[Environment](./environment.md)** - Environment setup
- **[Testing](./testing.md)** - Pre-deployment testing
- **[Architecture](./architecture.md)** - System architecture

---
*This is a core atom - the single source of truth for deployment information.*
