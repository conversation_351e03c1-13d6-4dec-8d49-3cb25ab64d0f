---
title: "Architecture - Core Atom"
type: "atom"
purpose: "System architecture and design decisions"
includes: ['Tech stack', 'Database design', 'Security', 'Performance']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# Architecture - Core Atom

## Purpose
Complete system architecture and design decisions for FAAFO Career Platform

## Quick Reference

### Key Components
- **Next.js 14** - Full-stack React framework with App Router
- **PostgreSQL + Prisma** - Database with type-safe ORM
- **NextAuth.js** - Authentication and session management
- **Vercel** - Deployment platform with edge functions

## Complete Guide

### Prerequisites
- Understanding of modern web architecture
- Familiarity with React and TypeScript
- Basic knowledge of database design

### Technology Stack

#### Frontend Stack
```typescript
// Core Technologies
- Next.js 14 (App Router)
- React 18
- TypeScript
- Tailwind CSS
- Radix UI (shadcn/ui)

// State Management
- React Context API
- React Query (TanStack Query)
- React Hook Form + Zod validation
```

#### Backend Stack
```typescript
// API Layer
- Next.js API Routes (serverless)
- RESTful endpoints
- Rate limiting middleware
- Error handling middleware

// Database
- PostgreSQL (production)
- SQLite (development)
- Prisma ORM
- Connection pooling
```

### Database Architecture

#### Core Models
```prisma
// User Management
model User {
  id                   String    @id @default(cuid())
  email                String    @unique
  password             String
  failedLoginAttempts  Int       @default(0)
  lockedUntil          DateTime?
  assessments          Assessment[]
  profile              Profile?
  forumPosts           ForumPost[]
}

// Assessment System
model Assessment {
  id          String               @id @default(uuid())
  userId      String
  status      AssessmentStatus     @default(IN_PROGRESS)
  currentStep Int                  @default(0)
  responses   AssessmentResponse[]
}

// Career System
model CareerPath {
  id              String   @id @default(uuid())
  name            String   @unique
  slug            String   @unique
  overview        String
  actionableSteps Json
  learningPaths   LearningPath[]
}
```

#### Design Principles
1. **Normalized structure** - Minimize data duplication
2. **Proper indexing** - Optimize query performance
3. **Cascade deletes** - Maintain referential integrity
4. **JSON fields** - Flexible data storage where appropriate
5. **UUID primary keys** - Distributed system compatibility

### Security Architecture

#### Authentication Flow
```typescript
// NextAuth.js Configuration
- Credentials provider with custom logic
- Password hashing with bcrypt (12 rounds)
- Failed attempt tracking (5 attempts, 15 min lockout)
- JWT tokens with 30-day expiration
- Secure session management
```

#### Security Measures
1. **Input Validation** - Zod schema validation
2. **SQL Injection Prevention** - Prisma ORM protection
3. **XSS Protection** - Content sanitization
4. **Rate Limiting** - API endpoint protection
5. **CSRF Protection** - Token-based validation

### Performance Architecture

#### Optimization Strategies
```typescript
// Frontend
- Server Components by default
- Client Components only when needed
- Dynamic imports for heavy components
- Image and font optimization

// Backend
- Connection pooling
- Query optimization
- Proper database indexing
- API response caching
```

### Best Practices

#### Code Organization
1. **Feature-based structure** - Group related functionality
2. **Type safety** - Comprehensive TypeScript usage
3. **Separation of concerns** - Clear responsibility boundaries
4. **Consistent patterns** - Standardized approaches

#### Development Workflow
1. **Test-driven development** - Write tests first
2. **Code reviews** - Peer review process
3. **Continuous integration** - Automated testing
4. **Documentation** - Keep architecture docs updated

### Common Issues

#### Performance Bottlenecks
- **Issue**: Slow database queries
- **Solution**: Add proper indexing and query optimization
- **Debug**: Use database query logging and analysis tools

#### Authentication Issues
- **Issue**: Session management problems
- **Solution**: Verify NextAuth configuration and session storage
- **Debug**: Check JWT token validation and expiration

### Related Atoms
- **[Environment](./environment.md)** - Infrastructure setup
- **[API](./api.md)** - API design patterns
- **[Deployment](./deployment.md)** - Production architecture

---
*This is a core atom - the single source of truth for architecture information.*
