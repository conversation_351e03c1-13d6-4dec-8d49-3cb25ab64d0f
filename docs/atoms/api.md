---
title: "API - Core Atom"
type: "atom"
purpose: "API documentation and usage"
includes: ['Endpoints', 'Authentication', 'Examples', 'Errors']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# API - Core Atom

## Purpose
Complete API documentation for FAAFO Career Platform including endpoints, authentication, and usage

## Quick Reference

### Key Components
- **RESTful Endpoints** - Next.js API routes with rate limiting
- **Authentication** - NextAuth.js with session management
- **Request/Response** - JSON with standardized error handling
- **Rate Limiting** - 100 requests per 15 minutes (configurable)

## Complete Guide

### Prerequisites
- Environment setup completed (see [Environment](./environment.md))
- Valid authentication session
- Understanding of REST principles

### Core API Architecture

#### Base Configuration
```typescript
// API Configuration
const API_CONFIG = {
  BASE_URL: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  RATE_LIMIT_REQUESTS: 100,
  RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000, // 15 minutes
};
```

#### Authentication System
**NextAuth.js with session-based authentication**

```javascript
// Check authentication status
const session = await getServerSession(authOptions);
if (!session?.user?.id) {
  return NextResponse.json(
    { success: false, error: 'Authentication required' },
    { status: 401 }
  );
}
```

**Session Structure:**
```typescript
interface Session {
  user: {
    id: string;
    email: string;
    name?: string;
    image?: string;
  };
  expires: string;
}
```

### Core API Endpoints

#### Authentication Endpoints
```bash
# Login
POST /api/auth/login
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password"
}

# Logout
POST /api/auth/logout

# Session check
GET /api/auth/session
```

#### User Management
```bash
# Get user profile
GET /api/user/profile
Authorization: Session required

# Update profile
PUT /api/user/profile
Content-Type: application/json
{
  "firstName": "John",
  "lastName": "Doe",
  "bio": "Career enthusiast"
}
```

#### Assessment System
```bash
# Start assessment
POST /api/assessment/start
Authorization: Session required

# Submit response
POST /api/assessment/response
Content-Type: application/json
{
  "questionKey": "career_interest",
  "answerValue": ["technology", "healthcare"]
}

# Get results
GET /api/assessment/results
Authorization: Session required
```

#### Learning Paths
```bash
# Get learning paths
GET /api/learning-paths?page=1&limit=10&category=technology
Rate Limit: 100 requests per 15 minutes

# Get specific path
GET /api/learning-paths/[id]
Rate Limit: 200 requests per 15 minutes

# Enroll in path
POST /api/learning-paths/[id]/enroll
Authorization: Session required
Rate Limit: 50 enrollments per 15 minutes
```

#### Career Paths
```bash
# Get career paths
GET /api/career-paths?industry=technology

# Bookmark career path
POST /api/career-paths/[id]/bookmark
Authorization: Session required
```

#### Forum System
```bash
# Get forum posts
GET /api/forum/posts?category=career-advice&page=1

# Create post
POST /api/forum/posts
Authorization: Session required
Content-Type: application/json
{
  "title": "Career transition advice",
  "content": "Looking for guidance...",
  "categoryId": "uuid",
  "tags": ["career-change", "advice"]
}

# React to post
POST /api/forum/posts/[id]/react
Authorization: Session required
Content-Type: application/json
{
  "type": "like"
}
```

### Request/Response Patterns

#### Standard Response Format
```typescript
// Success Response
{
  "success": true,
  "data": { /* response data */ },
  "cached"?: boolean,
  "pagination"?: {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}

// Error Response
{
  "success": false,
  "error": "Error message",
  "code"?: "ERROR_CODE",
  "details"?: { /* additional error info */ }
}
```

#### Pagination
```bash
# Standard pagination parameters
GET /api/endpoint?page=1&limit=10&sort=createdAt&order=desc

# Response includes pagination metadata
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "totalPages": 15,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### Rate Limiting

#### Default Limits
- **General endpoints**: 100 requests per 15 minutes
- **Authentication**: 50 requests per 15 minutes
- **Content creation**: 10 requests per 15 minutes
- **Enrollment actions**: 50 requests per 15 minutes

#### Rate Limit Headers
```bash
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

### Error Handling

#### HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request (validation error)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **429**: Too Many Requests (rate limited)
- **500**: Internal Server Error

#### Common Error Responses
```javascript
// Validation Error
{
  "success": false,
  "error": "Validation failed",
  "details": {
    "email": "Invalid email format",
    "password": "Password too short"
  }
}

// Rate Limit Error
{
  "success": false,
  "error": "Rate limit exceeded",
  "retryAfter": 900
}
```

### Best Practices

#### API Usage
1. **Always check response.success** before using data
2. **Handle rate limits** with exponential backoff
3. **Use pagination** for large datasets
4. **Cache responses** when appropriate
5. **Include error handling** for all requests

#### Security
1. **Never expose sensitive data** in API responses
2. **Validate all inputs** on the server side
3. **Use HTTPS** in production
4. **Implement proper CORS** policies
5. **Log security events** for monitoring

### Common Issues

#### Authentication Errors
- **Issue**: 401 Unauthorized responses
- **Solution**: Check session validity and refresh if needed
- **Debug**: Verify NextAuth configuration and session storage

#### Rate Limiting
- **Issue**: 429 Too Many Requests
- **Solution**: Implement exponential backoff and request queuing
- **Debug**: Check rate limit headers and adjust request frequency

#### Validation Errors
- **Issue**: 400 Bad Request with validation details
- **Solution**: Validate inputs on client side before sending
- **Debug**: Check API documentation for required fields and formats

#### Performance Issues
- **Issue**: Slow API responses
- **Solution**: Use pagination, caching, and database optimization
- **Debug**: Check database queries and enable query logging

### Related Atoms
- **[Environment](./environment.md)** - API configuration
- **[Testing](./testing.md)** - API testing strategies
- **[Architecture](./architecture.md)** - System design

---
*This is a core atom - the single source of truth for API information.*
