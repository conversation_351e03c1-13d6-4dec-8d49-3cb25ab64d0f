# Documentation Usage Report

Generated: 2025-06-15 22:25:26

## Summary

- **Total Files**: 127
- **Files with Dependencies**: 23
- **Total Dependencies**: 175
- **Orphaned Files**: 62

## Most Used Files

- **operations/README.md**: 8 references
- **project-management/03_TECH_SPECS.md**: 7 references
- **README.md**: 7 references
- **project-management/02_ARCHITECTURE.md**: 6 references
- **user-guides/API.md**: 6 references
- **atoms/setup/environment.md**: 5 references
- **project-management/00_PROJECT_OVERVIEW.md**: 5 references
- **user-guides/troubleshooting-guide.md**: 5 references
- **DOCUMENTATION_INDEX.md**: 5 references
- **user-guides/faq-troubleshooting.md**: 4 references

## Orphaned Files

- archives/DOCUMENTATION_UPDATE_JUNE_2025_20250615.md
- project-management/ORGANIZATION_COMPLETE_SUMMARY.md
- development/ENHANCED_ASSESSMENT_RESULTS_IMPLEMENTATION.md
- testing/legacy/SECURITY_FIXES_IMPLEMENTATION_REPORT.md
- workflows/documentation-migration.md
- features/MAJOR_DUPLICATION_CLEANUP.md
- archives/DOCUMENTATION_ORGANIZATION_COMPLETE_JUNE_2025.md
- archives/DOCUMENTATION_UPDATE_SUPER_TESTERATOR_JUNE_2025_20250615.md
- archives/DOCUMENTATION_UPDATE_COMPREHENSIVE_JUNE_2025.md
- atoms/procedures/url-validation.md
- development/IMPLEMENTATION_COMPLETE.md
- STYLE_GUIDE.md
- archives/UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK_20250615.md
- testing/legacy/ASSESSMENT_TESTING_SUMMARY.md
- testing/SUPER_TESTERATOR_FIXES_COMPLETE.md
- testing/reports/TESTING_SUMMARY_AND_RECOMMENDATIONS.md
- archives/DUPLICATE_PREVENTION_IMPLEMENTATION_SUMMARY_20250615.md
- archives/DOCUMENTATION_MIGRATION_GUIDE_20250615.md
- operations/DEPLOYMENT_CHECKLIST.md
- operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md
- development/PHASE1_IMPLEMENTATION_PLAN.md
- features/NAVIGATION_CLEANUP.md
- development/AI_POWERED_INSIGHTS_IMPLEMENTATION.md
- development/DOCUMENTATION_UPDATE_SUMMARY.md
- operations/VERCEL_DEPLOYMENT_GUIDE.md
- templates/DOCUMENT_TEMPLATE.md
- archives/SYSTEMATIC_DUPLICATE_PREVENTION_PROTOCOL_20250615.md
- development/LEARNING_RESOURCES_PAGINATION_FIX_JUNE_2025.md
- atoms/concepts/validation-system.md
- atoms/commands/testerat.md
- testing/reports/FINAL_TEST_EXECUTION_REPORT.md
- archives/DOCUMENTATION_ORGANIZATION_SYSTEM_20250615.md
- operations/VERCEL_DEPLOYMENT_SUMMARY.md
- archives/DOCUMENTATION_UPDATE_DATABASE_MIGRATION_20250615.md
- api/FREEDOM_FUND_API_VERIFICATION.md
- testing/legacy/PROFILE_TESTING_CHECKLIST.md
- testing/legacy/EMAIL_VERIFICATION_TESTING_GUIDE.md
- testing/reports/COMPREHENSIVE_TESTING_ANALYSIS.md
- features/PROGRESS_ANALYTICS_CONSOLIDATION.md
- development/AGENT_TRANSITION_SUMMARY_JUNE_12_2025.md
- archives/ULTIMATE_PROJECT_MANAGEMENT_PROTOCOL_20250615.md
- development/INCOMPLETE_IMPLEMENTATIONS_COMPLETED.md
- development/AI_INSIGHTS_IMPLEMENTATION_COMPLETE.md
- reference/migration-summary.md
- archives/UNIVERSAL_DUPLICATE_PREVENTION_TEMPLATE_20250615.md
- archives/DOCUMENTATION_UPDATE_ASSESSMENT_TESTING_20250615.md
- operations/COMPLETE_CLEANUP_GUIDE.md
- archives/DOCUMENTATION_REORGANIZATION_SUMMARY_20250615.md
- PROJECT_NAVIGATION_SYSTEM.md
- development/PHASE1_IMPLEMENTATION_COMPLETE.md
- development/PHASE1_SETUP_GUIDE.md
- testing/legacy/ASSESSMENT_TESTING_PLAN.md
- resource-improvement-summary.md
- atoms/commands/testing.md
- testing/reports/TESTING_INFRASTRUCTURE_FIXED.md
- archives/DOCUMENTATION_UPDATE_NEXTJS_DOWNGRADE_JUNE_2025_20250615.md
- archives/DOCUMENTATION_ORGANIZATION_SUMMARY_20250615.md
- archives/IMPLEMENTATION_COMPLETE_SUMMARY_20250615.md
- features/RESOURCE_GAP_ANALYSIS_IMPLEMENTATION.md
- testing/legacy/COMPREHENSIVE_ASSESSMENT_TESTING_REPORT.md
- testing/legacy/DASHBOARD_TEST_REPORT.md
- atoms/concepts/testing-structure.md

