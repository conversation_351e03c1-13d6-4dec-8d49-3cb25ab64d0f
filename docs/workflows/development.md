---
title: "Development - Complete Workflow"
type: "workflow"
purpose: "Daily development workflow"
composes: ['environment', 'testing', 'deployment']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# Development - Complete Workflow

## Purpose
Daily development workflow

## Workflow Overview
This workflow combines multiple atoms to provide a complete process.

### Composed Atoms
- [Environment](../atoms/environment.md)
- [Testing](../atoms/testing.md)
- [Deployment](../atoms/deployment.md)

## Daily Development Workflow

### Step 1: Start Development Session (5 minutes)
**Goal**: Prepare development environment for productive work

1. **Update Codebase**
   ```bash
   git checkout main
   git pull origin main
   npm install  # Update dependencies if needed
   ```

2. **Start Development Services**
   ```bash
   npm run dev  # Start Next.js development server
   # In another terminal:
   npx prisma studio  # Database browser (optional)
   ```

3. **Verify Environment**
   - [ ] http://localhost:3000 loads correctly
   - [ ] No console errors in browser
   - [ ] Database accessible

### Step 2: Feature Development (Main Work)
**Goal**: Implement features following best practices

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Development Process**
   ```bash
   # Make changes to code
   # Test changes in browser
   npm test  # Run tests frequently
   npm run type-check  # Check TypeScript
   ```

3. **Code Quality Checks**
   ```bash
   npm run lint  # Fix linting issues
   npm run format  # Format code (if available)
   ```

4. **Database Changes (if needed)**
   ```bash
   # If modifying schema:
   npx prisma migrate dev --name your-migration-name
   npx prisma generate
   ```

### Step 3: Testing & Validation (15 minutes)
**Goal**: Ensure changes work correctly and don't break existing functionality

1. **Unit Testing**
   ```bash
   npm test  # Run all tests
   npm test -- --watch  # Watch mode during development
   npm run test:coverage  # Check coverage
   ```

2. **Manual Testing**
   - [ ] Test your specific changes
   - [ ] Test related functionality
   - [ ] Test on different screen sizes
   - [ ] Check browser console for errors

3. **Integration Testing**
   ```bash
   # Test API endpoints if changed
   curl http://localhost:3000/api/health

   # Test database operations
   npx prisma studio
   ```

4. **End-to-End Testing (for major changes)**
   ```bash
   # Run comprehensive testing
   python3 testerat http://localhost:3000 "feature testing"
   ```

### Step 4: Code Review Preparation (10 minutes)
**Goal**: Prepare code for review and potential deployment

1. **Final Quality Check**
   ```bash
   npm run build  # Test production build
   npm run type-check  # Final TypeScript check
   npm test  # All tests must pass
   ```

2. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: descriptive commit message"
   git push origin feature/your-feature-name
   ```

3. **Create Pull Request**
   - Open GitHub and create PR
   - Add descriptive title and description
   - Request review from team members
   - Link any related issues

### Step 5: Deployment (when ready)
**Goal**: Deploy tested changes to production

1. **Merge to Main**
   ```bash
   # After PR approval:
   git checkout main
   git pull origin main
   git branch -d feature/your-feature-name
   ```

2. **Production Deployment**
   - Vercel automatically deploys from main branch
   - Monitor deployment in Vercel dashboard
   - Verify production functionality

3. **Post-Deployment Verification**
   ```bash
   # Test production endpoints
   curl https://your-domain.com/api/health

   # Run production tests
   python3 testerat https://your-domain.com "production verification"
   ```

## Success Criteria
- [ ] Feature implemented and working locally
- [ ] All tests passing
- [ ] No TypeScript or linting errors
- [ ] Production build successful
- [ ] Code reviewed and approved
- [ ] Successfully deployed to production
- [ ] Production verification complete

## Best Practices

### Code Quality
1. **Write tests first** (TDD approach)
2. **Keep commits small** and focused
3. **Use descriptive commit messages**
4. **Follow TypeScript strict mode**
5. **Maintain test coverage >80%**

### Database Changes
1. **Always create migrations** for schema changes
2. **Test migrations** on development data
3. **Backup production** before major changes
4. **Use reversible migrations** when possible

### Performance
1. **Test with realistic data** volumes
2. **Monitor API response times**
3. **Optimize database queries**
4. **Use React DevTools** for component optimization

## Troubleshooting

### Development Issues
1. **Hot reload not working**
   ```bash
   # Restart development server
   npm run dev
   ```

2. **Database out of sync**
   ```bash
   npx prisma migrate reset
   npx prisma migrate dev
   ```

3. **TypeScript errors**
   ```bash
   npx prisma generate  # Regenerate Prisma client
   npm run type-check  # Check specific errors
   ```

### Testing Issues
1. **Tests failing after changes**
   - Update test snapshots if UI changed
   - Check for async timing issues
   - Verify test data setup

2. **Coverage dropping**
   - Add tests for new functionality
   - Remove dead code
   - Check test configuration

**Need Help?** See [Troubleshooting Atom](../atoms/troubleshooting.md) for detailed solutions.

## Related Resources
- **[Testing Guide](../atoms/testing.md)** - Comprehensive testing strategies
- **[API Documentation](../atoms/api.md)** - API endpoint details
- **[Deployment Guide](../atoms/deployment.md)** - Production deployment
- **[Architecture Overview](../atoms/architecture.md)** - System design

---
*This workflow composes 3 atoms into a complete process.*
