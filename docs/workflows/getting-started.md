---
title: "Getting-Started - Complete Workflow"
type: "workflow"
purpose: "Complete onboarding for new developers"
composes: ['environment', 'testing']
last_updated: "2025-06-15"
maintainer: "development-team"
---

# Getting-Started - Complete Workflow

## Purpose
Complete onboarding for new developers

## Workflow Overview
This workflow combines multiple atoms to provide a complete process.

### Composed Atoms
- [Environment](../atoms/environment.md)
- [Testing](../atoms/testing.md)

## Step-by-Step Process

### Step 1: Environment Setup (30 minutes)
**Goal**: Get FAAFO development environment running locally

1. **Prerequisites Check**
   ```bash
   node --version  # Should be 18+
   git --version   # Should be 2.0+
   ```

2. **<PERSON>lone and Install**
   ```bash
   git clone https://github.com/dm601990/faafo.git
   cd faafo/faafo-career-platform
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with required values:
   # NEXTAUTH_SECRET="your-secret-key"
   # NEXTAUTH_URL="http://localhost:3000"
   # DATABASE_URL="file:./dev.db"
   ```

4. **Database Setup**
   ```bash
   npx prisma migrate dev
   npx prisma generate
   npm run prisma:seed  # Optional test data
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   # Open http://localhost:3000
   ```

### Step 2: Verify Installation (15 minutes)
**Goal**: Confirm everything works correctly

1. **Test Core Features**
   - [ ] Homepage loads at http://localhost:3000
   - [ ] User registration works
   - [ ] Login/logout functions
   - [ ] Assessment system accessible
   - [ ] Freedom Fund calculator works

2. **Test Database**
   ```bash
   npx prisma studio  # Opens database browser
   # Verify tables exist and data is present
   ```

3. **Run Basic Tests**
   ```bash
   npm test  # Should pass all tests
   npm run type-check  # Should have no TypeScript errors
   ```

### Step 3: Development Workflow Setup (15 minutes)
**Goal**: Understand daily development process

1. **Learn Key Commands**
   ```bash
   npm run dev        # Start development server
   npm test           # Run tests
   npm run lint       # Check code quality
   npm run build      # Test production build
   ```

2. **Understand Project Structure**
   ```
   faafo-career-platform/
   ├── src/app/           # Next.js app router pages
   ├── src/components/    # React components
   ├── src/lib/          # Utilities and configurations
   ├── prisma/           # Database schema and migrations
   └── __tests__/        # Test files
   ```

3. **Set Up Development Tools**
   - Install recommended VS Code extensions
   - Configure Git hooks (optional)
   - Set up database browser bookmarks

## Success Criteria
- [ ] Development server runs without errors
- [ ] Can create account and log in
- [ ] All core features accessible
- [ ] Tests pass successfully
- [ ] Database connection working
- [ ] Understand project structure
- [ ] Know key development commands

## Troubleshooting
**Common Issues:**

1. **Port 3000 in use**
   ```bash
   npm run dev -- -p 3001
   ```

2. **Database connection errors**
   ```bash
   rm -f prisma/dev.db
   npx prisma migrate dev
   ```

3. **Node version issues**
   - Install Node.js 18+ from nodejs.org
   - Consider using nvm for version management

4. **Permission errors**
   ```bash
   sudo chown -R $(whoami) ~/.npm
   ```

**Need Help?** See [Troubleshooting Atom](../atoms/troubleshooting.md) for detailed solutions.

## Next Steps
After completing this workflow:
1. **[Development Workflow](./development.md)** - Learn daily development process
2. **[Testing Guide](../atoms/testing.md)** - Understand testing approach
3. **[API Documentation](../atoms/api.md)** - Learn API endpoints
4. **[Architecture Overview](../atoms/architecture.md)** - Understand system design

---
*This workflow composes 2 atoms into a complete process.*
