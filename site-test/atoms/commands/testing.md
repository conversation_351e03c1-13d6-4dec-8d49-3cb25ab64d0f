---
ai_context: Standard testing commands for FAAFO Career Platform
built_at: '2025-06-15T22:17:44.464775'
category: atoms
dependencies: []
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: testing-team
source_file: atoms/commands/testing.md
subcategory: commands
tags:
- testing
- commands
- jest
- playwright
title: Testing Commands
used_by: []
---

## Testing Commands

### Unit Tests
```bash
# Run all unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run specific test suite
npm run test:integration -- --testNamePattern="Assessment API"
```

### End-to-End Tests
```bash
# Run E2E tests
npm run test:e2e

# Run E2E tests in headed mode
npm run test:e2e:headed

# Run specific E2E test
npx playwright test assessment-flow
```

### Test Database
```bash
# Reset test database
npm run test:db:reset

# Seed test data
npm run test:db:seed
```

### Testerat (AI Testing Tool)
```bash
# Run comprehensive AI testing
cd testerat
python testerat.py --comprehensive

# Run specific test category
python testerat.py --category="authentication"
```
