---
ai_context: Standard development commands for FAAFO Career Platform
built_at: '2025-06-15T22:25:14.438555'
category: atoms
dependencies: []
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: development-team
source_file: atoms/commands/development.md
subcategory: commands
tags:
- development
- commands
- npm
- scripts
title: Development Commands
used_by: []
---

## Development Commands

### **Start Development Server**
```bash
cd faafo-career-platform
npm run dev
```
Server runs at: http://localhost:3000

### **Build & Production**
```bash
# Build for production
npm run build

# Start production server
npm start

# Check build status
npm run build:check
```

### **Code Quality**
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check

# Format code
npm run format
```

### **Database Commands**
```bash
# Database studio
npm run db:studio

# Reset database
npm run db:reset

# Run migrations
npm run db:migrate

# Seed database
npm run db:seed
```

### **Verification**
```bash
# Health check
npm run health-check

# Expected output:
# ✅ Server running on port 3000
# ✅ Database connected
# ✅ All services operational
```
