---
ai_context: Essential environment setup steps for FAAFO Career Platform development
built_at: '2025-06-15T22:40:07.329152'
category: atoms
dependencies: []
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: development-team
source_file: atoms/setup/environment.md
subcategory: setup
tags:
- environment
- setup
- development
- prerequisites
title: Environment Setup
used_by: []
---

## Environment Setup

### Prerequisites
- Node.js 18+ installed
- Git configured
- Code editor (VS Code recommended)

### Required Environment Variables

Create a `.env.local` file in the `faafo-career-platform` directory:

```bash
# Database
DATABASE_URL="your_database_url"

# Authentication
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"

# Email Service
RESEND_API_KEY="your_resend_api_key"

# AI Services
GOOGLE_GEMINI_API_KEY="your_gemini_api_key"

# Monitoring
SENTRY_DSN="your_sentry_dsn"
```

### Verification

Run this command to verify your environment:

```bash
cd faafo-career-platform
npm run env:check
```

Expected output:
```
✅ Node.js version: 18.x.x
✅ Environment variables loaded
✅ Database connection successful
```
