---
ai_context: Standard directory structure for FAAFO Career Platform documentation
built_at: '2025-06-15T22:35:53.809682'
category: atoms
dependencies: []
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: documentation-team
source_file: atoms/concepts/directory-structure.md
subcategory: concepts
tags:
- structure
- organization
- directories
- docs
title: Documentation Directory Structure
used_by: []
---

## Documentation Directory Structure

### **Single Source of Truth**
ALL documentation MUST be placed in the root-level `docs/` directory.

```
docs/
├── README.md                          # Documentation hub overview
├── project-management/                # Planning, requirements, architecture
│   ├── 00_PROJECT_OVERVIEW.md
│   ├── 01_REQUIREMENTS.md
│   ├── 02_ARCHITECTURE.md
│   ├── 03_TECH_SPECS.md
│   ├── 04_UX_GUIDELINES.md
│   ├── 05_DATA_POLICY.md
│   └── 07_PROJECT_STATUS.md
├── development/                       # Implementation guides, decisions
│   ├── README.md
│   ├── IMPLEMENTATION_*.md
│   └── PHASE*_*.md
├── testing/                          # Test strategies, reports, guides
│   ├── README.md
│   ├── core/
│   ├── api-testing/
│   ├── reports/
│   └── legacy/
├── user-guides/                      # End-user and API documentation
│   ├── README.md
│   ├── user-guide.md
│   ├── API.md
│   └── troubleshooting-guide.md
├── operations/                       # Deployment, maintenance, monitoring
│   ├── README.md
│   ├── deployment.md
│   ├── database-backup.md
│   └── maintenance.md
├── features/                         # Feature specifications
│   ├── README.md
│   └── *.md
└── templates/                        # Document templates
    └── DOCUMENT_TEMPLATE.md
```

### **Forbidden Locations**
NEVER create documentation in these locations:
- `faafo-career-platform/docs/` ❌
- `src/docs/` ❌
- Root-level `.md` files (except README.md) ❌
- Component-level documentation ❌
- Random directories ❌
