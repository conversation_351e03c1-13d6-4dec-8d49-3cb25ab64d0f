---
ai_context: Systematic workflow for migrating legacy documentation to atomic design
  system
built_at: '2025-06-15T22:40:07.370665'
category: workflows
dependencies:
- atoms/concepts/directory-structure.md
- atoms/concepts/naming-conventions.md
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: documentation-team
source_file: workflows/documentation-migration.md
tags:
- migration
- documentation
- atomic-design
- cleanup
title: Documentation Migration Workflow
used_by: []
---

# Documentation Migration Workflow

This workflow guides the systematic migration of legacy documentation files to the new atomic design system.

## Migration Strategy

### **Phase 1: Analysis & Planning**

1. **Identify Legacy Files**
```bash
# Find problematic files
find docs/ -name "DOCUMENTATION_*" -type f
find docs/ -name "*_SUMMARY.md" -type f
find docs/ -name "*_COMPLETE.md" -type f
```

2. **Analyze Content Overlap**
```bash
# Generate usage graph
python3 scripts/generate-usage-graph.py

# Check for orphaned files
python3 scripts/validate-includes.py
```

### **Phase 2: Content Extraction**

## Documentation Directory Structure

### **Single Source of Truth**
ALL documentation MUST be placed in the root-level `docs/` directory.

```
docs/
├── README.md                          # Documentation hub overview
├── project-management/                # Planning, requirements, architecture
│   ├── 00_PROJECT_OVERVIEW.md
│   ├── 01_REQUIREMENTS.md
│   ├── 02_ARCHITECTURE.md
│   ├── 03_TECH_SPECS.md
│   ├── 04_UX_GUIDELINES.md
│   ├── 05_DATA_POLICY.md
│   └── 07_PROJECT_STATUS.md
├── development/                       # Implementation guides, decisions
│   ├── README.md
│   ├── IMPLEMENTATION_*.md
│   └── PHASE*_*.md
├── testing/                          # Test strategies, reports, guides
│   ├── README.md
│   ├── core/
│   ├── api-testing/
│   ├── reports/
│   └── legacy/
├── user-guides/                      # End-user and API documentation
│   ├── README.md
│   ├── user-guide.md
│   ├── API.md
│   └── troubleshooting-guide.md
├── operations/                       # Deployment, maintenance, monitoring
│   ├── README.md
│   ├── deployment.md
│   ├── database-backup.md
│   └── maintenance.md
├── features/                         # Feature specifications
│   ├── README.md
│   └── *.md
└── templates/                        # Document templates
    └── DOCUMENT_TEMPLATE.md
```

### **Forbidden Locations**
NEVER create documentation in these locations:
- `faafo-career-platform/docs/` ❌
- `src/docs/` ❌
- Root-level `.md` files (except README.md) ❌
- Component-level documentation ❌
- Random directories ❌


## Documentation Naming Conventions

### **Prefixes by Type**
- `IMPLEMENTATION_*.md` - Implementation guides
- `TESTING_*.md` - Testing documentation
- `ASSESSMENT_*.md` - Assessment-related docs
- `DATABASE_*.md` - Database documentation
- `DEPLOYMENT_*.md` - Deployment guides
- `PHASE*_*.md` - Phase-specific documentation

### **Suffixes by Purpose**
- `*_GUIDE.md` - Step-by-step guides
- `*_SUMMARY.md` - Summary documents
- `*_REPORT.md` - Reports and analysis
- `*_PLAN.md` - Planning documents
- `*_STRATEGY.md` - Strategy documents

### **Special Files**
- `README.md` - Directory overview (one per directory)
- `00_PROJECT_OVERVIEW.md` - Project overview (numbered for ordering)
- `01_REQUIREMENTS.md` - Requirements (numbered for ordering)

### **Examples**
- ✅ `TESTING_STRATEGY.md`
- ✅ `IMPLEMENTATION_GUIDE.md`
- ✅ `ASSESSMENT_RESULTS_SUMMARY.md`
- ✅ `DATABASE_MIGRATION_PLAN.md`
- ❌ `test-stuff.md`
- ❌ `random-notes.md`
- ❌ `temp-doc.md`

### **Quick Check**
Before creating any .md file, ask:
1. What category does this belong to?
2. Is there an existing file I should update instead?
3. Does this belong in the correct `docs/` subdirectory?
4. Does the filename follow our conventions?


### **Phase 3: Atomic Content Creation**

For each legacy file:

1. **Extract Reusable Content**
   - Setup procedures → `atoms/setup/`
   - Commands → `atoms/commands/`
   - Concepts → `atoms/concepts/`
   - Procedures → `atoms/procedures/`

2. **Create Atomic Files**
```bash
# Example atomic content creation
docs/atoms/setup/environment.md
docs/atoms/commands/testing.md
docs/atoms/concepts/validation-system.md
docs/atoms/procedures/deployment-checklist.md
```

3. **Add Proper Metadata**
```yaml
---
title: "Clear, Descriptive Title"
category: "atoms"
subcategory: "setup|commands|concepts|procedures"
tags: ["tag1", "tag2", "tag3"]
last_updated: "2025-06-15"
last_validated: "2025-06-15"
dependencies: []
used_by: []
maintainer: "team-name"
ai_context: "Brief description for AI understanding"
---
```

### **Phase 4: Workflow Composition**

1. **Create Complete Workflows**
```bash
# Example workflows
docs/workflows/development-setup.md
docs/workflows/testing.md
docs/workflows/deployment.md
```

2. **Use Transclusion**
```markdown
## Environment Setup

### Prerequisites
- Node.js 18+ installed
- Git configured
- Code editor (VS Code recommended)

### Required Environment Variables

Create a `.env.local` file in the `faafo-career-platform` directory:

```bash
# Database
DATABASE_URL="your_database_url"

# Authentication
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"

# Email Service
RESEND_API_KEY="your_resend_api_key"

# AI Services
GOOGLE_GEMINI_API_KEY="your_gemini_api_key"

# Monitoring
SENTRY_DSN="your_sentry_dsn"
```

### Verification

Run this command to verify your environment:

```bash
cd faafo-career-platform
npm run env:check
```

Expected output:
```
✅ Node.js version: 18.x.x
✅ Environment variables loaded
✅ Database connection successful
```

## Testing Commands

### Unit Tests
```bash
# Run all unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run specific test suite
npm run test:integration -- --testNamePattern="Assessment API"
```

### End-to-End Tests
```bash
# Run E2E tests
npm run test:e2e

# Run E2E tests in headed mode
npm run test:e2e:headed

# Run specific E2E test
npx playwright test assessment-flow
```

### Test Database
```bash
# Reset test database
npm run test:db:reset

# Seed test data
npm run test:db:seed
```

### Testerat (AI Testing Tool)
```bash
# Run comprehensive AI testing
cd testerat
python testerat.py --comprehensive

# Run specific test category
python testerat.py --category="authentication"
```

```

### **Phase 5: Legacy File Archival**

1. **Move to Archives**
```bash
# Archive legacy files
mv docs/DOCUMENTATION_*.md docs/archives/
mv docs/*_SUMMARY.md docs/archives/
mv docs/*_COMPLETE.md docs/archives/
```

2. **Update References**
```bash
# Find and update broken links
python3 scripts/validate-includes.py
# Fix broken references to point to new atomic content
```

## Migration Checklist

### **Per Legacy File**
- [ ] Content analyzed for reusable components
- [ ] Atomic content extracted and created
- [ ] Proper metadata added to all atoms
- [ ] Workflows created that compose atoms
- [ ] Legacy file moved to archives
- [ ] References updated

### **System-Wide**
- [ ] All includes validate successfully
- [ ] No orphaned content
- [ ] Build system works without errors
- [ ] Usage graph shows proper relationships
- [ ] Documentation count reduced significantly

## Quality Gates

### **Before Migration**
- Legacy file contains useful content
- Content is not duplicated elsewhere
- Migration plan documented

### **After Migration**
- All atomic content has proper metadata
- Workflows compose correctly
- No broken includes or references
- Build system validates successfully

## Success Metrics

### **Quantitative**
- **File Count**: Reduced from 114 to 30-40 files
- **Metadata Completeness**: 100% (up from 10%)
- **Broken Links**: 0 (down from 74)
- **Orphaned Files**: < 5 (down from 54)

### **Qualitative**
- Clear navigation paths
- Consistent structure
- Reusable content blocks
- AI-optimized organization

## Validation Commands

```bash
# Validate metadata
python3 scripts/validate-metadata.py

# Validate includes
python3 scripts/validate-includes.py

# Build documentation
python3 scripts/build-composed-docs.py

# Generate usage graph
python3 scripts/generate-usage-graph.py
```

## Rollback Plan

If migration causes issues:

1. **Restore from Archives**
```bash
# Restore specific file
cp docs/archives/LEGACY_FILE.md docs/

# Restore all if needed
cp docs/archives/*.md docs/
```

2. **Validate System**
```bash
# Check system health
python3 scripts/validate-includes.py
```

---

**Migration Status**: In Progress
**Target Completion**: Week 3
**Quality Standard**: 100% validation passing
