---
ai_context: Complete development environment setup workflow for FAAFO Career Platform
built_at: '2025-06-15T22:25:14.486918'
category: workflows
dependencies:
- atoms/setup/environment.md
- atoms/setup/database-setup.md
- atoms/commands/development.md
last_updated: '2025-06-15'
last_validated: '2025-06-15'
maintainer: development-team
source_file: workflows/development-setup.md
tags:
- development
- setup
- workflow
- onboarding
title: Complete Development Setup Workflow
used_by: []
---

# Complete Development Setup Workflow

This workflow guides you through setting up a complete development environment for the FAAFO Career Platform.

## Prerequisites

- Node.js 18+ installed
- Git configured with your credentials
- Code editor (VS Code recommended)
- Terminal access

## Step 1: Environment Configuration

## Environment Setup

### Prerequisites
- Node.js 18+ installed
- Git configured
- Code editor (VS Code recommended)

### Required Environment Variables

Create a `.env.local` file in the `faafo-career-platform` directory:

```bash
# Database
DATABASE_URL="your_database_url"

# Authentication
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"

# Email Service
RESEND_API_KEY="your_resend_api_key"

# AI Services
GOOGLE_GEMINI_API_KEY="your_gemini_api_key"

# Monitoring
SENTRY_DSN="your_sentry_dsn"
```

### Verification

Run this command to verify your environment:

```bash
cd faafo-career-platform
npm run env:check
```

Expected output:
```
✅ Node.js version: 18.x.x
✅ Environment variables loaded
✅ Database connection successful
```


## Step 2: Project Setup

### Clone and Install
```bash
# Clone the repository
git clone https://github.com/dm601990/faafo.git
cd faafo/faafo-career-platform

# Install dependencies
npm install

# Verify installation
npm run health-check
```

## Step 3: Database Configuration

## Database Setup

### **Development Database (SQLite)**
```bash
cd faafo-career-platform

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Seed database with test data
npx prisma db seed
```

### **Production Database (PostgreSQL)**
```bash
# Set DATABASE_URL in .env.local
DATABASE_URL="postgresql://username:password@host:port/database"

# Run migrations
npx prisma migrate deploy

# Generate client
npx prisma generate
```

### **Database Studio**
```bash
# Open Prisma Studio for database management
npx prisma studio
```

### **Reset Database**
```bash
# Reset development database
npx prisma migrate reset

# Confirm with 'y' when prompted
```

### **Verification**
```bash
# Check database connection
npm run db:check

# Expected output:
# ✅ Database connection successful
# ✅ All migrations applied
# ✅ Seed data loaded
```


## Step 4: Development Commands

## Development Commands

### **Start Development Server**
```bash
cd faafo-career-platform
npm run dev
```
Server runs at: http://localhost:3000

### **Build & Production**
```bash
# Build for production
npm run build

# Start production server
npm start

# Check build status
npm run build:check
```

### **Code Quality**
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check

# Format code
npm run format
```

### **Database Commands**
```bash
# Database studio
npm run db:studio

# Reset database
npm run db:reset

# Run migrations
npm run db:migrate

# Seed database
npm run db:seed
```

### **Verification**
```bash
# Health check
npm run health-check

# Expected output:
# ✅ Server running on port 3000
# ✅ Database connected
# ✅ All services operational
```


## Step 5: Verification

### Test Your Setup
```bash
# Start development server
npm run dev

# In another terminal, run tests
npm test

# Check code quality
npm run lint
npm run type-check
```

### Access Points
- **Application**: http://localhost:3000
- **Database Studio**: http://localhost:5555 (when running `npm run db:studio`)
- **API Documentation**: http://localhost:3000/api/docs

## Step 6: First Development Cycle

### Make Your First Change
1. Create a new branch: `git checkout -b feature/your-feature`
2. Make changes to the code
3. Run tests: `npm test`
4. Commit changes: `git commit -m "feat: your feature"`
5. Push branch: `git push origin feature/your-feature`
6. Create pull request

### Development Best Practices
- Always run tests before committing
- Follow the established code style
- Write meaningful commit messages
- Keep pull requests focused and small

## Troubleshooting

### Common Issues

**Port 3000 already in use:**
```bash
# Kill process on port 3000
npx kill-port 3000
# Or use different port
PORT=3001 npm run dev
```

**Database connection errors:**
- Check DATABASE_URL in .env.local
- Ensure database is running
- Try resetting: `npm run db:reset`

**Build errors:**
- Clear node_modules: `rm -rf node_modules && npm install`
- Clear Next.js cache: `rm -rf .next`
- Check Node.js version: `node --version`

### Getting Help

- Check existing documentation in `docs/`
- Review GitHub issues
- Ask in team chat
- Contact development team lead

## Success Criteria

✅ Development server starts without errors
✅ Database connection successful
✅ All tests pass
✅ Code linting passes
✅ Can access application at localhost:3000
✅ Can make and test code changes

---

**Next Steps**: After setup, see [Testing Workflow](testing.md) for quality assurance procedures.
